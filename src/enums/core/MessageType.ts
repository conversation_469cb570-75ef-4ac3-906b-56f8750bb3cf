export const enum MessageTypeEnum {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  FILE = 'file',
}

export const getMessageTypeEnum = (messageType: string): MessageTypeEnum | null => {
  switch (messageType) {
    case 'text':
      return MessageTypeEnum.TEXT

    case 'image':
      return MessageTypeEnum.IMAGE

    case 'video':
      return MessageTypeEnum.VIDEO

    case 'audio':
      return MessageTypeEnum.AUDIO

    case 'document':
      return MessageTypeEnum.DOCUMENT

    case 'file':
      return MessageTypeEnum.FILE

    default:
      return null
  }
}
