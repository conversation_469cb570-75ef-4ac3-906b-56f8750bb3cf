export const enum SniperSessionStatusEnum {
  OPENED = 'opened',
  PAUSED = 'paused',
  CLOSED = 'closed',
}

export type SniperSessionStatusTypes =
  | SniperSessionStatusEnum.OPENED
  | SniperSessionStatusEnum.PAUSED
  | SniperSessionStatusEnum.CLOSED

export const getStatusFromString = (status: string) => {
  switch (status) {
    case 'opened':
      return SniperSessionStatusEnum.OPENED
    case 'paused':
      return SniperSessionStatusEnum.PAUSED
    case 'closed':
      return SniperSessionStatusEnum.CLOSED
    default:
      return null
  }
}
