import cors from 'cors'
import * as dotenv from 'dotenv'
import express from 'express'
import helmet from 'helmet'
import { createServer } from 'http'
import { Server, Socket } from 'socket.io'
import { EvolutionController } from './controllers'
import { CodeChatInstanceStatusEnum } from './enums'
import { socketResponseModel } from './libs/model/socketResponse'
import { SocketAuthMiddleware } from './middlewares/socket/auth'
// import { SocketRulesMiddleware } from './middlewares/socket/rules'
import { RabbitMQService } from './platforms/rabbitmq/services/rabbitmq.service'
import * as routes from './routes'

// .ENV

dotenv.config()

// Express Server
// ---------------------
const app = express()

app.use(helmet())
app.use(express.json()) // body en formato json
app.use(express.urlencoded({ extended: false })) //body formulario

// SET VIEW ENGINE TO SHOW HTML ON INSTAGRAM AUTH
app.set('view engine', 'ejs'); // or 'pug', 'handlebars', etc.

// CORS - Allowed Origins
// ---------------------
// const allowedOrigins = ['*']
app.use(cors({ origin: true }))

// Routes
// ---------------------
app.use(...Object.values(routes))
app.get('/', (_req, res) => {
  res.send('All good! Server is running here =)')
})

const httpServer = createServer()
app.listen(process.env.SERVER_PORT || 8000, () => {
  console.log('%c Server running', 'color: green')
})

const io = new Server(httpServer)
httpServer.listen(process.env.SOCKET_PORT)

export const rabbitService = new RabbitMQService()
rabbitService.connect()

process.on('exit', () => {
  rabbitService.close()
})

interface ISocketClient {
  id: string
  socketId: string
  instance: string
  step: ISocketStepsEnum
}

export const enum ISocketStepsEnum {
  AWAIT = 'awaiting',
  CONNECT = 'connect',
  DISCONNECT = 'close',
}

// Verifica se o token e valido
io.use(SocketAuthMiddleware)
// Verifica as regras
// io.use(SocketRulesMiddleware)

const socketClients: ISocketClient | any = []

export const getSocketClientById = (id: string) => {
  return socketClients.find((client: ISocketClient) => client.id === id)
}
export const getSocketClientByInstance = (instance: string) => {
  return socketClients.find((client: ISocketClient) => client.instance === instance)
}

const saveSocketClient = (client: ISocketClient) => {
  socketClients.push(client)
}

const updateSocketClient = (client: ISocketClient) => {
  const index = socketClients.findIndex((c: ISocketClient) => c.id === client.id)
  socketClients[index] = client
}

const removeSocketClient = (id: string) => {
  const index = socketClients.findIndex(
    (client: ISocketClient) => client.id === id
  )
  socketClients.splice(index, 1)
}

export const ioSendData = (key: string, data: any) => {
  io.emit(key, data)
}

const onConnection = async (socket: Socket) => {
  const { query } = socket.handshake

  const { clientId } = query
  const connectionId = String(clientId)
  const response = socketResponseModel

  // Verifica se o ID existe
  if (!clientId) {
    response.error = true
    response.message = 'clientId not found'
    response.state = 'close'
    response.context = 'socket'
    socket.emit(connectionId, response)
    socket.disconnect()
    return
  }

  // Preciso salvar o ID do socket para devolver o QrCode no webhook
  saveSocketClient({
    id: connectionId,
    socketId: socket.id,
    instance: '',
    step: ISocketStepsEnum.AWAIT
  })

  socket.on('disconnect', () => {
    console.log('|| socket.io || disconnect ||')
    removeSocketClient(connectionId)
    socket.disconnect()
  })

  socket.on('platform-connect', async (instance) => {
    const { id, platform } = instance

    // Verifica se o ID existe
    if (!id || !platform) {
      socket.disconnect()
      return
    }

    switch (platform) {
      case 'Whatsapp':
        return EvolutionController.connectInstance(instance)
      default:
        response.error = true
        response.message = 'Platform not implemented'
        response.context = 'platform'
        response.state = CodeChatInstanceStatusEnum.CLOSE
        // Retorna a resposta
        socket.emit(id, response)
        break
    }
  })

  socket.on('platform-disconnect', async (instance) => {
    const { id, platform } = instance
    const response = socketResponseModel

    switch (platform) {
      case 'Whatsapp':
        return EvolutionController.disconnectInstance(instance)
      default:
        response.error = true
        response.message = 'Platform not implemented'
        response.context = 'platform'
        response.state = CodeChatInstanceStatusEnum.CLOSE
        // Retorna a resposta
        socket.emit(id, response)
        break
    }
  })
}

io.on('connection', onConnection)
