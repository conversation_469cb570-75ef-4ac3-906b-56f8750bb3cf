export const authenticationError = (error: any) => {
  const code = error.code
  let message = ''
  switch (code) {
    case 'auth/argument-error':
      console.log('🗝️ Invalid token', error.message)
      message = 'Invalid token'
      break

    case 'auth/id-token-expired':
      console.log('🗝️ Token expired', error.message)
      message = 'Token expired'
      break

    default:
      console.log('🗝️ Failed to authenticate', error)
      message = 'Failed to authenticate'
      break
  }

  return message
}
