import { isCyclic, isLoopable } from './objectUtils'

export function isDefined(
  variable: any,
  acceptNull: boolean,
  acceptEmpty: boolean,
  acceptZero: boolean,
  acceptNaN: boolean
) {
  if (acceptNull === true && variable === null) return true
  if (acceptEmpty === true && variable === '') return true
  if (acceptZero === true && variable === 0) return true

  if (isNaN(variable) && typeof variable === 'number' && acceptNaN !== true)
    return false // NaN returns number from typeof

  return (
    variable !== undefined &&
    variable !== null &&
    variable !== '' &&
    variable !== 0
  )
}

export const replaceUndefined = (obj: any, replacement?: unknown) => {
  if (isLoopable(obj)) {
    Object.keys(obj).forEach((key) => {
      if (obj[key] === undefined || typeof obj[key] === 'undefined') {
        obj[key] = replacement || null
      } else if (isLoopable(obj[key]) && !isCyclic(obj[key])) {
        obj[key] = replaceUndefined(obj[key], replacement)
      }
    })
  }
  return obj
}
