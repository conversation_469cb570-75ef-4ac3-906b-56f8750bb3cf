import moment from 'moment'
import { DATA_ORIGINS, SHOTX_COLLECTION_NAME } from '../constants/collections'
import { TRIGGERS } from '../constants/triggers'
import { MessageTypeEnum } from '../enums/codechat/MessageType'
import { InstagramEventEnum } from '../enums/instagram/Event'
import { InstanceStatusEnum } from '../enums/instance/Status'
import { ContextModel } from '../libs/model/context'
import { LeadModel } from '../libs/model/lead'
import { AccountRepository } from '../libs/repositories/firestore/account'
import { ContactRepository } from '../libs/repositories/firestore/contact'
import { InstagramRepository } from '../libs/repositories/firestore/instagram'
import { InstancesRepository } from '../libs/repositories/firestore/instance'
import { ShotXCronRepository } from '../libs/repositories/firestore/shotXCron'
import { MinioRepository } from '../libs/repositories/minio/api'
import { InstagramService } from '../libs/services/axios/instagram'
import { saveShotxCronLogs } from '../libs/services/firestore/logs'
import { instanceCreateType } from '../libs/types/codechat/instanceCreate'
import { InstagramCommentEvent } from '../libs/types/instagram/CommentEvent'
import {
  InstagramMessageEntry,
  InstagramMessageEvent,
} from '../libs/types/instagram/MessageEvent'
import { InstagramContactType } from '../libs/types/instance/contact'
import { InstanceType } from '../libs/types/instance/instance'
import { MinioFile } from '../libs/types/minio/file'
import { InteractionRepository } from '../platforms/interaction/repositories/interaction.repository'
import { SniperController } from '../platforms/sniper/controllers/sniper.controller'
import { SniperResponse } from '../platforms/sniper/types/sniper.response'
import { SessionRef } from '../platforms/sniper/types/sniper.session'
import { getExtensionFromMimeType } from '../utils/audio'
import { dateToString, getTimestampNow } from '../utils/dateUtils'
import { checkEventType } from '../utils/instagramUtils'
import { mergeObject } from '../utils/objectUtils'
import LeadController from './lead'
import LogsController from './logs'

class InstagramController {
  private logs
  constructor(
    private readonly service: InstagramService,
    private readonly repository: InstagramRepository,
    private readonly instances: InstancesRepository,
    private readonly accounts: AccountRepository,
    private readonly contacts: ContactRepository,
    private readonly minio: MinioRepository,
    private readonly sniper: SniperController,
    private readonly interactions: InteractionRepository
  ) {
    this.logs = LogsController
  }

  async webhookReceived(body: InstagramCommentEvent | InstagramMessageEvent) {
    const { entry, object } = body
    // console.log('INSTAGRAM WEBHOOK RECEIVED', body)

    if (object !== 'instagram') {
      console.log('Event not from Instagram', JSON.stringify(object))
      return
    }

    const entries = Object(entry)

    entries.forEach((entry: any) => {
      const eventType = checkEventType(entry)

      switch (eventType) {
        case InstagramEventEnum.COMMENTS:
          this.onEventReceived(entry)
          break
        case InstagramEventEnum.MENSSAGE:
          this.onMessageReceived(entry)
          break
        default:
          console.log('Unknown event received ', JSON.stringify(body))
          break
      }
    })
  }

  async auth(accountId: string, instanceId: string) {
    return this.service.getAuthenticationUrl(accountId, instanceId)
  }

  async authForce(
    newInstance: any,
    longToken: string,
    userProfile: any,
    oldInstance: any,
    accountId: string,
    permissions: any,
    action: string
  ) {
    const { id, user_id, name, username, account_type, profile_picture_url } =
      userProfile
    const ig_id = String(user_id)

    if (action === 'confirm') {
      try {
        const accountGetResponse = await this.accounts.get(accountId)
        const account = accountGetResponse.data
        const ig_ids = [...new Set([...(account.ig_ids || []), ig_id])]

        // Atualiza os ids do instagram associados a essa conta
        await this.accounts.update(accountId, { ig_ids })

        const oldInstanceNewDoc = {
          ...oldInstance,
          auth: {},
          status: {
            ...oldInstance?.status,
            state: 'close',
          },
        }

        await this.instances.update(oldInstanceNewDoc)

        const newInstanceNewDoc = {
          ...newInstance,
          ig_id,
          auth: {
            longToken,
            permissions,
          },
          status: {
            ...newInstance?.status,
            app_scope_id: id,
            ig_id,
            username,
            profile: name,
            profilePicture: profile_picture_url,
            account_type,
            state: 'open',
            statusReason: 200,
          },
        }

        console.log('newInstanceNewDoc', newInstanceNewDoc)

        await this.instances.update(newInstanceNewDoc)

        if (newInstanceNewDoc.subscriptions) {
          this.service.subscribeToWebhook(
            longToken,
            newInstanceNewDoc.subscriptions
          )
        } else {
          this.service.unsubscribeToWebhook(longToken)
        }

        return {
          error: false,
          data: 'ok',
        }
      } catch (error) {
        return {
          error: true,
          data: error,
        }
      }
    } else {
      return {
        error: false,
        data: 'ok',
      }
    }
  }

  async authCallback(
    accountId: string,
    instanceId: string,
    code: string
    // user: any
  ) {
    // 1. OBTEM O TOKEN DO USUARIO
    const tokenResponse = await this.service.getUserToken(code)
    // console.log('TOKEN RESPONSE', tokenResponse)

    if (tokenResponse.error) {
      return tokenResponse
    }

    const { access_token, permissions } = tokenResponse?.data

    // 2. COM O TOKEN DO USUARIO OBTEM O TOKEN DE LONGA DURATION
    const longTokenResponse = await this.service.getLongLivedToken(access_token)
    // console.log('LONG RESPONSE', longTokenResponse)
    const longToken = longTokenResponse.data.access_token

    if (longTokenResponse.error) {
      return longTokenResponse
    }

    // 2. COM O TOKEN DE LONGA DURATION, BUSCA OS DADOS DO USUARIO
    const userResponse = await this.service.getUserProfile(longToken)
    // console.log('PROFILE RESPONSE', userResponse)
    const userProfile = userResponse.data
    if (userResponse.error) {
      return userResponse
    }

    const { id, user_id, name, username, account_type, profile_picture_url } =
      userProfile
    const ig_id = String(user_id)

    // TODO: https://agilethink-icaro.atlassian.net/browse/QIP-335
    const instanceByIgSid = await this.instances.findInstanceByIgSid(ig_id)
    const instanceResponse = await this.instances.get(accountId, instanceId)
    // console.log('INSTANCE RESPONSE', instanceResponse)
    const instanceDoc = instanceResponse.data
    if (instanceResponse.error && instanceDoc) {
      return instanceResponse
    }

    if (instanceByIgSid) {
      return {
        error: false,
        data: {
          oldInstance: instanceByIgSid,
          longToken,
          userProfile,
          newInstance: instanceDoc,
          permissions,
        },
      }
    }
    // if (instance.id != instanceId) {

    // }

    const accountGetResponse = await this.accounts.get(accountId)
    // console.log('ACCOUNT RESPONSE', accountGetResponse)
    const account = accountGetResponse.data
    const ig_ids = [...new Set([...(account.ig_ids || []), ig_id])]

    // Atualiza os ids do instagram associados a essa conta
    this.accounts.update(accountId, { ig_ids })

    const updateDoc = {
      ...instanceDoc,
      ig_id,
      auth: {
        longToken,
        permissions,
      },
      status: {
        ...instanceDoc?.status,
        app_scope_id: id,
        ig_id,
        username,
        profile: name,
        profilePicture: profile_picture_url,
        account_type,
        state: 'open',
        statusReason: 200,
      },
    }

    const instanceUpdateResponse = await this.instances.update(updateDoc)

    const { oldInstance } = instanceUpdateResponse

    await this.logs.createLogs(
      instanceUpdateResponse,
      'instance_connected',
      null, // user,
      oldInstance
    )

    if (updateDoc.subscriptions) {
      this.service.subscribeToWebhook(longToken, updateDoc.subscriptions)
    } else {
      this.service.unsubscribeToWebhook(longToken)
    }

    return {
      error: false,
      data: instanceUpdateResponse,
    }
  }

  async logout(instance: instanceCreateType) {
    const {
      subscriptions = [],
      status: { state = 'close' },
      auth: { longToken },
    } = instance
    try {
      const resultUnsubscribe =
        await this.service.unsubscribeToWebhook(longToken)

      const updateDoc = {
        ...instance,
        auth: {
          longToken: '',
          permissions: [],
        },
        status: {
          ...instance?.status,
          state: 'close',
        },
      }

      const result = await this.instances.update(updateDoc)

      this.logs.createLogs(
        result,
        'instance_disconnected',
        null,
        result.oldInstance
      )

      return {
        error: false,
        status: 201,
        data: result,
      }
    } catch (error) {
      return {
        error: true,
        status: 500,
        data: error,
      }
    }
  }

  async updateWebhooks(instance: instanceCreateType) {
    try {
      const {
        subscriptions = [],
        status: { state = 'close' },
        auth: { longToken },
      } = instance
      console.log('updateWebhooks', { subscriptions, state, longToken })
      let result
      if (subscriptions.length > 0 && state === InstanceStatusEnum.CONNECTED) {
        console.log('inscrever', { subscriptions, state })
        result = await this.service.subscribeToWebhook(longToken, subscriptions)
      } else {
        console.log('desinscrever', { subscriptions, state })
        result = await this.service.unsubscribeToWebhook(longToken)
      }

      return {
        error: false,
        status: 201,
        data: result,
      }
    } catch (error) {
      return {
        error: true,
        status: 500,
        data: error,
      }
    }
  }

  async onEventReceived(entry: any) {
    const ig_id = String(entry.id)
    const change = entry.changes[0]
    const values = change.value

    const eventDoc = {
      id: values.id,
      text: values?.text || '',
      senderId: values.from.id,
      senderName: values.from.username,
      media: values.media,
      eventType: change.field,
      eventFrom: 'Instagram',
      timestamp: entry.time,
      entry: entry,
    }

    const instance = await this.instances.findInstanceByIgSid(ig_id)
    const instanceId = instance?.id
    const subscriptions = instance?.subscriptions

    if (!instanceId) {
      console.error('Error saving event, instance not found')
      return
    } else if (!subscriptions.includes('comments')) {
      console.error('Error saving message, event doest exist')
      return
    }

    const { accountId } = instance

    const contactResponse = await this.contacts.get({
      accountId,
      instanceId,
      contact: { ig_sid: values.from.id } as any,
    })
    if (contactResponse.error) {
      console.error('Error saving event, contact not found')
      return
    }

    if (contactResponse.data) {
      const contactUpdate = contactResponse.data
      const { event_type } = contactUpdate

      if (!event_type.includes('comments')) {
        contactUpdate.event_type.push('comments')
        this.contacts.update({
          accountId,
          instanceId,
          contact: contactUpdate,
        })
      }
    } else {
      const username = values.from.username
      const contact = {
        ig_sid: values.from.id,
        created_at: getTimestampNow(),
        event_type: ['comments'],
        username,
      } as InstagramContactType
      this.contacts.save({
        accountId,
        instanceId,
        contact,
      })
    }

    this.repository.saveEvent(accountId, instanceId, eventDoc, values.id)
  }

  async onMessageReceived(entry: InstagramMessageEntry) {
    const ig_id = String(entry.id)
    console.log('INSTAGRAM MESSAGE RECEIVED FROM:', ig_id)

    const instance = await this.instances.findInstanceByIgSid(ig_id)
    console.log('INSTANCE FOUND? ', !!instance)
    const instanceId = instance?.id
    const accessToken = instance?.auth?.longToken || instance?.auth?.jwt
    const subscriptions = instance?.subscriptions

    if (!instanceId) {
      console.error('Error saving message, instanceId not found')
      return
    } else if (!subscriptions.includes('messages')) {
      console.error('Error saving message, subscription does not exist')
      return
    }

    const { accountId } = instance

    const messageData = entry.messaging[0]
    let { message, read, reaction, timestamp, recipient, sender, postback } =
      messageData

    const received = ig_id == recipient.id
    const senderId = received ? sender.id : recipient.id

    if (read) {
      // Evento de mensagem lida

      const messageDoc = await this.repository
        .getMessageDocReference(
          instance.accountId,
          instanceId,
          senderId,
          read.mid
        )
        .get()

      const messageData = messageDoc.data()

      if (messageData?.shotxCron && !messageData.read) {
        const lead = await this.getVinculatedLead(
          accountId,
          instanceId,
          senderId
        )
        saveShotxCronLogs(
          messageData?.shotxCron.id,
          instance.accountId,
          instanceId,
          lead.id,
          TRIGGERS.READ,
          read.mid
        )
      }

      this.repository.updateMessage(
        instance.accountId,
        instanceId,
        senderId,
        read.mid,
        {
          read: true,
        }
      )

      return
    }

    if (reaction) {
      // Evento de mensagem lida

      const messageDoc = await this.repository
        .getMessageDocReference(
          instance.accountId,
          instanceId,
          senderId,
          reaction.mid
        )
        .get()

      const messageData = messageDoc.data()

      if (messageData?.shotxCron && !messageData.read) {
        const lead = await this.getVinculatedLead(
          accountId,
          instanceId,
          senderId
        )
        saveShotxCronLogs(
          messageData?.shotxCron.id,
          instance.accountId,
          instanceId,
          lead.id,
          TRIGGERS.READ,
          reaction.mid
        )
      }
      this.repository.updateMessage(
        instance.accountId,
        instanceId,
        senderId,
        reaction.mid,
        {
          read: true,
          reaction,
        }
      )

      return
    }

    if (postback) {
      // Evento recebido quando o usuário clica em uma opção
      message = {
        mid: postback.mid,
        text: postback.payload,
      }
      messageData.message = message
    }

    // Busca o objeto Contact
    let contact = { ig_sid: senderId } as InstagramContactType
    const contactResponse = await this.contacts.get({
      accountId,
      instanceId,
      contact,
    })

    if (contactResponse.error) {
      console.error('ERROR ON GET CONTACT', contactResponse.message)
      return
    }

    // Obtem os dados do usuário do Instagram
    const userProfile = await this.service.getUserDataFromSenderID(
      senderId,
      accessToken
    )

    if (userProfile.error) {
      console.log('ERROR ON GET USER PROFILE', userProfile)
    }

    const { name = '', username, profile_pic } = userProfile.data
    if (contactResponse.data) {
      const mergeData = {
        username,
        name,
        avatar: profile_pic,
      }

      // Atualiza o objeto contact com os dados recebidos
      contact = mergeObject<InstagramContactType>(
        contactResponse.data,
        mergeData
      )

      const { event_type } = contact

      if (!event_type.includes('messaging')) {
        contact.event_type.push('messaging')
      }
      this.contacts.update({
        accountId,
        instanceId,
        contact,
      })
    } else {
      contact = {
        ig_sid: senderId,
        created_at: getTimestampNow(),
        event_type: ['messaging'],
        username,
        name,
        avatar: profile_pic,
        ig_sid_owner: instance.ig_id,
      } as InstagramContactType
      this.contacts.save({
        accountId,
        instanceId,
        contact,
      })
    }

    const messageType = messageData.message?.text
      ? MessageTypeEnum.TEXT
      : MessageTypeEnum.ATTACHMENT

    const messageDoc = {
      id: entry.id,
      messageId: message.mid,
      message: message?.text || '',
      senderId: senderId,
      receiver: received,
      sender: !received,
      read: false,
      timestamp: timestamp / 1000,
      instagram: messageData,
      date: entry.time,
      instanceId,
      accountId,
      type: messageType,
      // profile: userProfile?.data
    }

    this.repository.saveMessage(
      accountId,
      instanceId,
      senderId,
      message?.mid,
      messageDoc,
      true
    )

    if (received && message.text) {
      // SALVAR LOGS PARA MENSAGENS RESPONDIDAS PELO BROADCAST
      const lastFromMe = await this.repository.getLastFromMe(
        accountId,
        instanceId,
        senderId
      )

      console.log('lastFromMe', lastFromMe)

      if (
        lastFromMe &&
        lastFromMe.shotxCron &&
        lastFromMe.shotxCron.replied == false
      ) {
        await this.repository.saveMessage(
          accountId,
          instanceId,
          senderId,
          lastFromMe.messageId,
          {
            shotxCron: {
              replied: true,
            },
          },
          true
        )

        const lead = await this.getVinculatedLead(
          accountId,
          instanceId,
          senderId
        )

        saveShotxCronLogs(
          lastFromMe?.shotxCron.id,
          instance.accountId,
          instanceId,
          lead.id,
          TRIGGERS.REPLIED,
          lastFromMe.messageId
        )
      }
      // Responder apenas mensagem de text por enquanto
      const response = await this.sniper.messageReceived(
        instance,
        senderId,
        username, // var contactRemoteId
        name, // var contactName
        message.text,
        messageType
      )

      this.sendReply(senderId, accessToken, response.messages)
    }
  }

  async sendReply(
    senderId: string,
    accessToken: string,
    messages: SniperResponse[]
  ) {
    for (const { type, message } of messages) {
      switch (type) {
        case 'text':
          await this.service.sendMessageText(senderId, message, accessToken)
          break

        case 'image':
        case 'video':
        case 'audio':
          await this.service.sendAttachment(
            senderId,
            type,
            message,
            accessToken
          )
          break
        case 'button':
          await this.service.sendButton(senderId, message, accessToken)
          break
        default:
          break
      }
    }
  }

  async updateStatus(instanceId: string, accountId: string, status: string) {
    const response = await this.instances.get(accountId, instanceId)
    if (response.error) {
      return response
    }
    const instanceUpdated = {
      ...response.data,
      status: {
        ...response.data?.status,
        state: status,
      },
    }
    await this.instances.update(instanceUpdated)
  }

  async unsubscribe(access_token: string) {
    return this.service.unsubscribeToWebhook(access_token)
  }

  async sendMedia(
    instance: InstanceType,
    ig_sid: string,
    file: MinioFile,
    mediaType: string
  ) {
    console.log('sendMedia', file)

    const { accountId, id } = instance
    if (!accountId || !id) {
      return {}
    }

    const fileToSend = file
    if (mediaType === 'audio') {
      return this.sendAudioFile(id, accountId, ig_sid, file)
    }

    if (fileToSend.mimetype === 'audio/ogg') {
      return {
        error: true,
        data: {
          message: 'Fail to convert, invalid format',
        },
      }
    }

    console.log('uploadFile', fileToSend)
    // const mediaUrl = "https://getsamplefiles.com/download/m4a/sample-1.m4a"
    const mediaUrl = await this.minio.uploadFile(
      accountId,
      id,
      ig_sid,
      mediaType,
      fileToSend
    )

    console.log('uploadFile', mediaUrl)
    const sendResult = await this.service.sendAttachment(
      ig_sid,
      mediaType,
      mediaUrl,
      instance.auth?.longToken
    )
    console.log('sendAttachment', sendResult)

    if (sendResult.error) {
      const { message, response } = sendResult.data
      console.log('FALHA AO ENVIAR ARQUIVO', { message, response })
      return sendResult
    }

    return sendResult
  }

  async sendAudioFile(
    instanceId: string,
    accountId: string,
    contactId: string,
    audio: MinioFile
  ) {
    const now = new Date()
    const date = dateToString(now, 'YYYYMMDDTHHMMSS')

    const extension = getExtensionFromMimeType(audio.mimetype)
    audio.originalname = `${date}.${extension}`

    // try {
    //   const convert = await processAudioBufferToMp4(audio.buffer)
    //   console.log('Convert', convert)
    //   if (convert) {
    //     audio.buffer = convert
    //     audio.mimetype = 'audio/mp4'
    //     audio.encoding = 'aac'
    //   }
    // } catch (error) {
    //   console.log('Falha ao converter arquivo', error)
    // }

    const midiaUrl = await this.minio.uploadFile(
      accountId,
      instanceId,
      contactId,
      'audio',
      audio
    )
    console.log('uploadFile', midiaUrl)
    const sendResult = await this.service.sendAttachment(
      instanceId,
      accountId,
      contactId,
      midiaUrl
    )

    if (sendResult.error) {
      console.log('FALHA AO ENVIAR O AUDIO', sendResult)
      return sendResult
    }

    return sendResult
  }

  async sendMessage(
    senderId: string,
    message: string,
    instance: any,
    shotxCron?: any
  ) {
    const response = await this.service
      .sendMessageText(senderId, message, instance.auth.longToken)
      .then((res) => {
        console.log('SEND MESSAGE', res)

        if (shotxCron) {
          const messageDoc = {
            id: instance.ig_id,
            messageId: res.data.message.message_id,
            message: message,
            senderId: instance.ig_id,
            receiver: senderId,
            sender: true,
            read: false,
            timestamp: getTimestampNow() / 1000,
            instagram: res.data,
            date: getTimestampNow(),
            instanceId: instance.id,
            accountId: instance.accountId,
            type: 'text',
            shotxCron,
            // profile: userProfile?.data
          }
          this.repository.saveMessage(
            instance.accountId,
            instance.id,
            senderId,
            res.data.message.message_id,
            messageDoc
          )
        }
        return res
      })
      .catch((err) => {
        console.log('ERROR')
        const newResponse = {
          ...err.response,
        }
        if (newResponse.disconnect) {
          const instanceUpdated = {
            ...instance,
            auth: {
              ...instance.auth,
              longToken: '',
            },
            status: {
              ...instance.data?.status,
              state: InstanceStatusEnum.DISCONNETED,
            },
          }
          this.instances.update(instanceUpdated)
        }
        newResponse.data.message = err.message

        return newResponse
      })

    return response
  }

  public async getVinculatedLead(
    accountId: string,
    instanceId: string,
    contactId: string
  ) {
    const leads = await LeadController.findByIg_sid(
      accountId,
      instanceId,
      contactId
    )

    return leads.length ? leads[0] : null
  }

  public async onSniperSessionClose(session: SessionRef, matchedVars: any) {
    const { accountId, instanceId, contactId } = session

    let lead = await this.getVinculatedLead(accountId, instanceId, contactId)
    const isVinculated = !!lead

    if (!lead) {
      lead = await this.findLeadByContact(accountId, matchedVars)
    }

    console.log('LEAD FOUND?', !!lead)
    // Se encontrou um lead
    if (lead) {
      // Se ele ainda tiver vinculado, adicionar vinculo
      if (!isVinculated) {
        // Busca os dados do contato
        const contact = await this.interactions.get(
          accountId,
          instanceId,
          contactId
        )
        if (contact) {
          // Vincula o lead a interação
          this.interactions.vinculateLead(
            accountId,
            instanceId,
            contactId,
            lead.id
          )

          const { username, ig_sid_owner } = contact
          const ig_contacts = lead.ig_contacts || []
          ig_contacts.push({
            ig_sid: contactId,
            ig_username: username,
            instanceId,
            vinculated_at: moment().unix(),
            ig_sid_owner,
          })

          // Vincula o contato ao lead
          lead.ig_contacts = ig_contacts
          lead.ig_sids = ig_contacts.map(
            (c: any) => `${c.ig_sid}-${instanceId}`
          )
        }
      }

      // Atualizar o lead
      LeadController.updateLeadWithSniperResults(lead, matchedVars)
    } else {
      // Se não encontrou um lead, criar

      // Busca os dados do contato
      const contact = await this.interactions.get(
        accountId,
        instanceId,
        contactId
      )
      if (!contact) return

      const { name, username, ig_sid_owner } = contact
      const firstName = name?.split(' ')[0] || username
      const lastName = name?.split(' ').slice(-1)[0] || ''

      let leadToCreate = {
        ...LeadModel,
        firstName,
        lastName,
        displayName: name || username,
        mobile: matchedVars.phone,
        mobileCC: matchedVars.phoneCC,
        email: matchedVars.email,
        accountId,
        origin: DATA_ORIGINS.SHOTX.INSTAGRAM,
        instanceId,
        context: {
          ...ContextModel,
          id: accountId,
          collection: SHOTX_COLLECTION_NAME,
          origin: DATA_ORIGINS.SHOTX.INSTAGRAM,
          instanceId,
        },
        ig_contacts: [
          {
            ig_sid: contactId,
            ig_username: username,
            instanceId,
            vinculated_at: moment().unix(),
            ig_sid_owner,
          },
        ],
        ig_sids: [`${contactId}-${instanceId}`],
      }

      leadToCreate = mergeObject(leadToCreate, matchedVars)

      console.log('CRIANDO LEAD')
      await LeadController.createLead(leadToCreate)

      const createdLead = await this.getVinculatedLead(
        accountId,
        instanceId,
        contactId
      )
      const leadId = createdLead.id || createdLead.ID
      console.log('LEAD CRIADO', !!leadId)

      // Vincula o lead a interação
      if (leadId) {
        console.log('VINCULANDO LEAD AO CONTATO')
        this.interactions.vinculateLead(
          accountId,
          instanceId,
          contactId,
          leadId
        )
      }
    }
  }

  public async findLeadByContact(accountId: string, matchedVars: any) {
    const { email, mobile, mobileCC, phone, phoneCC } = matchedVars as any

    // Busca pelo mobile
    if (!!mobile && !!mobileCC) {
      const lead = await LeadController.findByMobile(
        accountId,
        mobile,
        mobileCC
      )
      console.log('LEAD COM MOBILE?', !!lead)
      if (lead) return lead
    }

    // Se não encontrou lead, busca pelo email
    if (email) {
      const lead = await LeadController.findByEmail(accountId, email)
      console.log('LEAD COM EMAIL?', !!lead)
      if (lead) return lead
    }

    // Se não encontrou lead, busca pelo phone
    if (!!phone && !!phoneCC) {
      const lead = await LeadController.findByPhone(accountId, phone, phoneCC)
      console.log('LEAD COM PHONE?', !!lead)
      if (lead) return lead
    }

    return
  }
  async sendBroadcast(shotXCron: any) {
    const shotXCronRepository = new ShotXCronRepository()
    const { message, instance, id, contacts } = shotXCron
    shotXCronRepository.update(id, {
      executing: true,
    })

    for (const contact of contacts) {
      if (!instance || !contact.contactRemoteId || !message) {
        saveShotxCronLogs(
          id,
          instance.accountId,
          instance.id,
          contact.id,
          TRIGGERS.ERROR,
          '',
          {
            error: true,
            data: 'instanceId, contact and message are required',
          }
        )
      } else {
        try {
          const result = await this.sendMessage(
            contact.contactRemoteId,
            message,
            instance,
            {
              id: id,
              contactId: contact.id,
              replied: false,
            }
          )
          if (result.error) {
            saveShotxCronLogs(
              id,
              instance.accountId,
              instance.id,
              contact.id,
              TRIGGERS.ERROR,
              '',
              result
            )
          } else {
            const message = result?.data.message?.message_id || null
            saveShotxCronLogs(
              id,
              instance.accountId,
              instance.id,
              contact.id,
              TRIGGERS.SEND_SUCCESS,
              message,
              result
            )
          }
        } catch (error: any) {
          const message = error?.message || error || null

          saveShotxCronLogs(
            id,
            instance.accountId,
            instance.id,
            contact.id,
            TRIGGERS.ERROR,
            '',
            message
          )
        }
      }
    }

    shotXCronRepository.update(id, {
      executing: false,
      executed: true,
    })
  }
}

export default new InstagramController(
  new InstagramService(),
  new InstagramRepository(),
  new InstancesRepository(),
  new AccountRepository(),
  new ContactRepository(),
  new MinioRepository(),
  new SniperController(),
  new InteractionRepository()
)
