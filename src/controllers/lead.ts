import { LeadRepository } from '../libs/repositories/firestore/lead'
import LeadType from '../libs/types/qiplus/lead'
import { mergeObject } from '../utils/objectUtils'
import { getPhoneNumberInfo } from '../utils/phone.utils'
import { sanitizeText } from '../utils/stringUtils'

class LeadController {
  private static leadRepository = new LeadRepository()
  public static phoneFields = ['mobile', 'phone']
  public static nameFields = ['firstName', 'lastName', 'displayName']

  constructor() { }

  static async all() {
    return this.leadRepository.all()
  }

  static async createLead(leadToCreate: any) {
    await this.leadRepository.createLead(leadToCreate)
  }

  static async get(leadId: string) {
    return this.leadRepository.get(leadId)
  }

  static async findByIg_sid(accountId: string, instanceId: string, ig_sid: string) {

    const conditions = [
      ['ig_sids', 'array-contains', `${ig_sid}-${instanceId}`]
    ] as any

    return this.leadRepository.getWhere(accountId, conditions)
  }

  static async findByEmail(accountId: string, email: string) {
    const leads = await this.leadRepository.findByEmail(accountId, email)
    return leads.length ? leads[0] : null
  }

  static async findByPhone(accountId: string, phoneNumber: string, countryCode: string) {
    const leads = await this.leadRepository.findByPhone(accountId, phoneNumber, countryCode)
    return leads.length ? leads[0] : null
  }

  static async findByMobile(accountId: string, mobilePhone: string, countryCode: string) {
    const leads = await this.leadRepository.findByMobile(accountId, mobilePhone, countryCode)
    return leads.length ? leads[0] : null
  }

  static async updateLeadWithSniperResults(leadToUpdate: LeadType, matchedVars: any) {

    leadToUpdate = mergeObject(leadToUpdate, matchedVars)

    this.leadRepository.update(leadToUpdate)
  }

  /**
   * Prepara os dados para atualizar o lead
   * Trata caracteres de nomes, formato de telefone
   * 
   * @param matchedVars Objeto contendo os dados pra atualizar
   * @example { phone: '+**************', firstName: ' Paulo Wender ;' } => { phone: '***********', phoneCC: '55', firstName: 'Paulo Wender' }
   */
  static sanitizeMatchedVarsToUpdateLead = (matchedVars: any) => {

    const fieldsToSanitize = [...this.nameFields]

    for (const [key, value] of Object.entries(matchedVars)) {
      if (fieldsToSanitize.includes(key)) {
        matchedVars[key] = sanitizeText(String(value))
      }

      if (this.phoneFields.includes(key) && value && typeof value == 'string') {
        const phoneInfo = getPhoneNumberInfo(value)
        matchedVars[key] = phoneInfo?.national
        matchedVars[`${key}CC`] = phoneInfo?.countryCode
      }
    }

    return matchedVars
  }
}

export default LeadController
