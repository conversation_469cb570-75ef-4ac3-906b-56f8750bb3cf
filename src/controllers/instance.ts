import { <PERSON><PERSON><PERSON>roller, InstagramController } from '.'
import { EvolutionInstanceStatus } from '../enums/evolution/InstanceStatus'
import { InstancesRepository } from '../libs/repositories/firestore/instance'
import { CodechatService } from '../libs/services/axios/codechat'
import { instanceCreateType } from '../libs/types/codechat/instanceCreate'
import { instanceUpdateType } from '../libs/types/codechat/instanceUpdate'
import LogsController from './logs'

class InstanceController {
  private evolution
  private instagram
  private logs
  private instanceExistCodes
  constructor(
    private readonly service: CodechatService,
    private readonly repository: InstancesRepository
  ) {
    this.evolution = EvolutionController
    this.instagram = InstagramController
    this.logs = LogsController

    this.instanceExistCodes = [200, 403, 500]
  }

  async create(instance: instanceUpdateType, user: any) {
    const instaceCreated = await this.repository.createInstance(instance)

    if (instaceCreated.error) return instaceCreated

    this.logs.createLogs(instaceCreated, 'instance_added', user)

    return instaceCreated
  }

  async connect(instance: instanceCreateType, user: any) {
    const {
      id,
      accountId,
      auth: { jwt },
    } = instance
    // Obtem o status da instância no codechat
    console.log('🗝️ Getting instance status', id)

    const statusResponse = await this.service.status(id, accountId, jwt)

    console.log('statusResponse', statusResponse)

    const exists =
      !statusResponse.error &&
      this.instanceExistCodes.includes(statusResponse.status) && statusResponse.data?.length
    const instanceStatus = statusResponse.data[0]

    console.log('exists', exists);

    const result = {
      error: false,
      status: 200,
      message: '',
      data: statusResponse,
      code: '',
    }

    if (!exists) {
      console.log('🗝️ Instance not found, creating ...', id)
      // Cria a instância no codechat
      const createResult = await this.createCodechatInstance(instance)
      // Se houve erro ao criar
      if (createResult.error) {
        result.error = true
        result.status = 500
        result.message = 'Erro ao conectar instância'
        result.data = createResult
        return result
      }

      console.log('🗝️ Instance created', id)
    }

    // Se já estiver conectada
    if (instanceStatus?.connectionStatus === EvolutionInstanceStatus.OPEN) {
      console.log('🗝️ Instance already connected', id)
      await this.repository.update({
        ...instance,
        status: {
          ...instance.status,
          state: instanceStatus.connectionStatus,
          statusReason: statusResponse.status,
        },
      })

      return result
    }

    console.log('🗝️ Connecting instance', id)
    // Se a instância existe
    const connectResponse = await this.service.connect(id, accountId, jwt)
    const { data } = connectResponse

    // Se houve erro ao conectar
    if (connectResponse.error) {
      console.log('🗝️ Instance not connected', id)
      result.error = true
      result.status = 500
      result.message = 'Erro ao conectar instância'
      result.data = connectResponse
      return result
    }

    console.log('🗝️ Connection requested', id)
    // Se a instância foi conectada
    result.message = 'Conexão solicitada com sucesso'
    result.data = data
    result.code = data?.code

    this.logs.createLogs(result, 'instance_connect', user)

    return result
  }

  async createCodechatInstance(instanceDoc: instanceCreateType) {
    const { id, accountId } = instanceDoc
    const apiResponse = await this.service.createInstance(id, accountId)

    if (apiResponse.error) {
      console.log('🗝️ Failed to create instance', apiResponse)
      return apiResponse
    }
    const { data } = apiResponse
    console.log('RESPOSTA CODECHAT', apiResponse)

    // TO DO CRIAR STATUS PADRÃO AO CREATEINSTANCE
    //instanceDoc.status.state = data?.instance?.status
    instanceDoc.auth.jwt = data?.hash

    console.log('🗝️ Update instance databse', id)
    const updateResult = await this.repository.update(instanceDoc)
    console.log('🗝️ Update result', updateResult)

    if (!updateResult) {
      console.log('🗝️ Failed to update instance', id)
      return {
        error: true,
        status: 500,
        message: 'Erro ao salvar instância no Banco de Dados',
        data: instanceDoc,
      }
    }

    console.log('🗝️ Created instance', id)
    return {
      error: false,
      status: 200,
      message: 'Instância criada com sucesso',
      data: instanceDoc,
    }
  }

  async instances(accountId: string) {
    return this.repository.getInstances(accountId)
  }

  async update(instance: instanceUpdateType, user: any) {
    const { platform } = instance

    const result = await this.repository.update(instance)

    const { oldInstance } = result

    switch (platform) {
      case 'Instagram':
        await this.instagram.updateWebhooks(instance)
        break
      default:
        break;
    }

    this.logs.createLogs(result, 'instance_updated', user, oldInstance)

    return result
  }

  async disconnect(instance: instanceCreateType, user: any) {
    const result = this.evolution.disconnectInstance(instance)

    this.logs.createLogs(instance, 'instance_disconnected', user)

    return result
  }
}

export default new InstanceController(
  new CodechatService(),
  new InstancesRepository()
)
