import { LogsRepository } from '../libs/repositories/firestore/log'

class LogsController {
  constructor(private readonly repository: LogsRepository) { }

  public async createLogs(
    logs: any,
    trigger: string,
    user: any,
    oldLogs?: any
  ) {

    const data = logs.data

    switch (trigger) {
      case 'instance_added':
        await this.repository.createInstanceLog(data, trigger, user, null)
        break
      case 'instance_updated':
        await this.repository.createInstanceLog(data, trigger, user, oldLogs)
        break
      case 'instance_connected':
        await this.repository.createInstanceLog(data, trigger, user)
        break
      case 'instance_disconnected':
        await this.repository.createInstanceLog(data, trigger, user)
        break
    }
  }
}

export default new LogsController(new LogsRepository())
