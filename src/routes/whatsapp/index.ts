import { Request, Response, Router } from 'express'
import multer from 'multer'
import { WhatsappController } from '../../controllers'
import { AuthMiddleware } from '../../middlewares/http/auth'
import { Sniper<PERSON>ontroller } from '../../platforms/sniper/controllers/sniper.controller'
import { WhatsappValidationsMiddleware } from '../../platforms/whatsapp/middlewares/whatsapp.validations.middleware'
const uploadFile = multer({
  preservePath: true,
  limits: {
    fileSize: 1000000 * 10,
  },
})

export const router = Router()

const RESOURCE = '/whatsapp'

router.post(
  `${RESOURCE}/webhook`,
  WhatsappValidationsMiddleware,
  async (req: Request, res: Response) => {
    const data = req.body

    const response = WhatsappController.webhookReceived(data)

    res.send('ok')
    // RabbitMQIntercept<object>({
    //   queueName: `${RESOURCE}/webhook`,
    //   data: req.body,
    //   onQueue: () => {
    //     res.send({
    //       query: req.query,
    //       body: req.body,
    //     })
    //   },
    //   onReceive: (data: any) => {
    //     WhatsappController.webhookReceived(data)
    //   },
    // })
  }
)

router.post(
  `${RESOURCE}/message/send/:instanceId`,
  // AuthMiddleware,
  // RulesMiddleware,
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { accountId, userId, phone, message } = req.body
    console.log('AZAP', instanceId, req.body)
    if (!instanceId || !accountId || !userId || !phone || !message) {
      res.status(400).send({
        error: true,
        data: 'instanceId, accountId, userId, phone and message are required',
      })
      return
    }

    const response: any = await WhatsappController.sendText(
      instanceId,
      phone,
      message,
      userId,
      accountId
    )

    res.send({
      error: response?.error || false,
      data: response.data,
    })
  }
)

router.post(
  `${RESOURCE}/message/broadcast`,
  async (req: Request, res: Response) => {
    // RabbitMQIntercept<any>({
    //   queueName: `${RESOURCE}/message/broadcast`,
    //   data: req.body,
    //   onQueue(sended) {
    //     res.send('ok')
    //   },
    //   async onReceive(data) {
    const { shotxCrons } = req.body
    await WhatsappController.sendBroadcast(shotxCrons)
    res.send('ok')
    //   },
    // })
  }
)

router.post(
  `${RESOURCE}/message/:messageId/resend/:instanceId`,
  async (req: Request, res: Response) => {
    const { messageId, instanceId } = req.params
    const { accountId, userId, phone } = req.body

    if (
      !messageId?.trim() ||
      !instanceId?.trim() ||
      !accountId?.trim() ||
      !userId?.trim() ||
      !phone?.trim()
    ) {
      res.status(400).send({
        error: true,
        data: 'messageId, instanceId, accountId, userId and phone are required.',
      })
      return
    }

    const response: any = await WhatsappController.resendText(
      instanceId,
      phone,
      userId,
      accountId,
      messageId
    )

    res.send({
      error: response?.error || false,
      data: response.data,
    })
  }
)

router.post(
  `${RESOURCE}/message/read/:instanceId/`,
  AuthMiddleware,
  //RulesMiddleware,
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { readMessages, accountId } = req.body

    if (!readMessages || readMessages.length === 0) {
      res.status(400).send({
        error: true,
        data: 'readMessages are required',
      })
      return
    }

    const response: any = await WhatsappController.markMessageAsRead(
      instanceId,
      accountId,
      readMessages
    )

    res.send({
      error: response?.error || false,
      data: response.data,
    })
  }
)

router.post(
  `${RESOURCE}/audio/send/:instanceId`,
  // AuthMiddleware, // TODO: uncomment when auth is ready
  // RulesMiddleware, // TODO: uncomment when auth is ready
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { accountId, userId, phone, audio } = req.body

    if (!instanceId || !accountId || !userId || !phone || !audio) {
      res.status(400).send({
        error: true,
        data: 'instanceId, accountId, userId, phone and audio are required',
      })
      return
    }

    const response: any = await WhatsappController.sendAudio(
      instanceId,
      accountId,
      phone,
      audio
    )

    res.send({
      error: response?.error || false,
      data: response.data,
    })
  }
)

router.post(
  `${RESOURCE}/media/get/:instanceId`,
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { accountId, messageId } = req.body

    let response: any

    console.log('chamou')

    if (!instanceId || !accountId || !messageId) {
      res.status(400).send({
        error: true,
        data: 'instanceId, accountId, messageId',
      })
      return
    }

    await WhatsappController.retrieverMediaMessage(
      instanceId,
      accountId,
      messageId
    )
    res.setHeader('Content-type', response.headers['content-type'])
    res.setHeader(
      'Content-Disposition',
      response.headers['content-disposition']
    )

    // const transform: Transform = response.data;
    // transform.pipe(res);
    res.send(response.data)
  }
)

router.put(
  `${RESOURCE}/sniper/change_session/:instanceId`,
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { accountId, action, jwt, contactId } = req.body

    let response: any

    if (!instanceId || !accountId || !action || !contactId) {
      res.status(400).send({
        error: true,
        data: 'instanceId, accountId, contactId and action are required',
      })
      return
    }

    const controller = new SniperController()
    response = await controller.setSessionStatus(
      instanceId,
      accountId,
      action,
      contactId
    )

    res.send({
      error: response?.error || false,
      data: response.data,
    })
  }
)

router.post(
  `${RESOURCE}/send_presence/:instanceId`,
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { accountId, presence, contactRemoteId, jwt } = req.body

    let response: any

    if (!instanceId || !accountId || !presence || !contactRemoteId) {
      res.status(400).send({
        error: true,
        data: 'instanceId, accountId, presence and contactRemoteId are required',
      })
      return
    }

    response = await WhatsappController.sendPresence(
      accountId,
      instanceId,
      contactRemoteId,
      presence,
      jwt
    )

    console.log('response', response)

    res.send({
      error: response?.error || false,
      data: response.data,
    })
  }
)

router.post(
  `${RESOURCE}/sniper/connect/:instanceId`,
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { accountId, publicSniperId, enabled, jwt } = req.body

    if (!instanceId || !accountId || !publicSniperId) {
      res.status(400).send({
        error: true,
        data: 'instanceId, accountId, publicSniperID, enabled required',
      })
      return
    }

    const response = await WhatsappController.sniperConnect(
      instanceId,
      accountId,
      publicSniperId,
      enabled,
      jwt
    )

    res.send({
      error: response?.error || false,
      data: response.data,
    })
  }
)

router.post(
  `${RESOURCE}/media/send/:instanceId`,
  uploadFile.single('file'),
  // AuthMiddleware, // TODO: uncomment when auth is ready
  // RulesMiddleware, // TODO: uncomment when auth is ready
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { accountId, userId, phone, type, caption, mimetype } = req.body
    let response: any
    const file = req.file

    if (!instanceId || !accountId || !userId || !phone || !type || !file) {
      res.status(400).send({
        error: true,
        data: 'instanceId, accountId, userId, phone, type and file are required',
      })
      return
    }

    if (type === 'audio') {
      response = await WhatsappController.sendAudioFile(
        instanceId,
        accountId,
        userId,
        phone,
        file
      )
    } else {
      response = await WhatsappController.sendFile(
        instanceId,
        accountId,
        userId,
        phone,
        file,
        type,
        caption
      )
    }

    res.send({
      error: response?.error || false,
      data: response.data,
    })
  }
)
export { router as whatsappRouter }
