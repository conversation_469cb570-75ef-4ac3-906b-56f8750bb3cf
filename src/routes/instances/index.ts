import { NextFunction, Request, Response, Router } from 'express'
import { InstanceController } from '../../controllers'
import { AuthMiddleware } from '../../middlewares/http/auth'
import { RulesMiddleware } from '../../middlewares/http/rules'

export const router = Router()

const RESOURCE = '/instance'

const ValidateParams = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {

  const { instance } = req.body

  if (!instance) {
    return res.status(400).send({
      error: true,
      data: {
        message: 'instance are required',
      },
    })
  }

  next()
}

router.post(
  `${RESOURCE}/create`,
  AuthMiddleware,
  ValidateParams,
  async (req: Request, res: Response) => {

    const result: any = await InstanceController.create(
      req.body.instance,
      req.user
    )

    const statusCode = result?.error === false ? 201 : result.status
    return res.status(statusCode).send({
      error: false,
      data: result.data,
    })
  }
)

router.put(
  `${RESOURCE}/update`,
  AuthMiddleware,
  ValidateParams,
  async (req: Request, res: Response) => {
    const result: any = await InstanceController.update(
      req.body.instance,
      req.user
    )

    return res.send(result)
  })

router.post(
  `${RESOURCE}/connect`,
  AuthMiddleware,
  ValidateParams,
  async (req: Request, res: Response) => {
    const { instance } = req.body

    const result: any = await InstanceController.connect(instance, req.user)

    res.status(200).send(result)
  }
)

router.get(
  `${RESOURCE}/list/:accountId`,
  async (req: Request, res: Response) => {
    const { accountId } = req.params
    const instances = await InstanceController.instances(accountId)

    res.status(200).send({
      error: false,
      data: {
        instances,
      },
    })
  }
)

router.delete(
  `${RESOURCE}/disconnect`,
  AuthMiddleware,
  RulesMiddleware,
  ValidateParams,
  async (req: Request, res: Response) => {
    const { instance } = req.body
    const response: any = await InstanceController.disconnect(
      instance,
      req.user
    )

    res.status(response.status).send({
      error: response.error || false,
      data: response.data,
    })
  }
)

export { router as instanceRouter }
