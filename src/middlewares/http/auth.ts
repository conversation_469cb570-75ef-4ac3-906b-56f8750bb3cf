/**
 * User Middlewares
 *
 * Middleware para verificar se o usuário esta autenticado
 *
 * */

import { NextFunction, Request, Response } from 'express'

import { authenticationError } from '../../utils/authError'
import { decodeJwt } from '../../utils/jwtUtils'

export const AuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const { authorization } = req.headers
  const token = authorization?.replace('Bearer ', '')

  if (!token) {
    console.log('🗝️ token is required')
    return res.status(400).send({
      error: true,
      data: 'token is required',
    })
  }

  // Verifica se o token é valido
  decodeJwt(token)
    .then((decodedUser) => {
      // Variável para armazenar o ID do usuário
      const userUid = decodedUser?.uid

      // Verifica se o ID do usuário existe
      if (!userUid) {
        console.log('🗝️ user not found')
        return res.status(400).send({
          error: true,
          data: 'user not found',
        })
      }

      // Se o token é valido, o usuário está autenticado
      req.user = decodedUser
      console.log('🗝️ Valid Token')
      next()
    })
    .catch((error) => {
      return res.status(400).send({
        error: true,
        data: authenticationError(error),
      })
    })
}
