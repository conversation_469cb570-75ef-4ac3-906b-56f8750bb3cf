
const logs = process.env.RABBITMQ_ENABLED_LOGS
const [DEBUG, ERROR, WARN, LOG, INFO] = (logs || '').split(',')

const log = (message?: any, ...optionalParams: any[]) => {
  if (LOG) console.log(message, optionalParams)
}

const warn = (message?: any, ...optionalParams: any[]) => {
  if (WARN) console.warn(message, optionalParams)
}

const info = (message?: any, ...optionalParams: any[]) => {
  if (INFO) console.info(message, optionalParams)
}

const error = (message?: any, ...optionalParams: any[]) => {
  if (ERROR) console.error(message, optionalParams)
}

const debug = (message?: any, ...optionalParams: any[]) => {
  if (DEBUG) console.debug(message, optionalParams)
}
export const RabbitMQLogger = {
  log,
  warn,
  info,
  error,
  debug
}