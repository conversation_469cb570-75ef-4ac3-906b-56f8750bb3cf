import amqplib, { Channel, Connection } from 'amqplib';
import { RabbitMQLogger } from '../helpers/rabbitmq.logger';

export class RabbitMQService {
  private connection: Connection | null = null;
  private channels: { [queueName: string]: Channel } = {};
  private logger = RabbitMQLogger

  // Método para inicializar a conexão ao RabbitMQ
  async connect(): Promise<void> {
    try {
      if (!process.env?.RABBITMQ_URI) {
        throw new Error('RABBITMQ_URI não informada no .env')
      }
      if (!this.connection) {
        this.connection = await amqplib.connect(process.env?.RABBITMQ_URI);
        this.logger.log("Conectado ao RabbitMQ");
      }
    } catch (error) {
      this.logger.error("Erro ao conectar ao RabbitMQ:", error);
    }
  }

  // Método para obter ou criar um canal para uma fila específica
  async getChannel(queueName: string): Promise<Channel | undefined> {
    try {
      if (!this.connection) {
        await this.connect();
      }

      if (!this.channels[queueName] && this.connection) {
        const channel = await this.connection.createChannel();
        await channel.assertQueue(queueName, { durable: true });
        this.channels[queueName] = channel;
        this.logger.log(`Canal criado para a fila: ${queueName}`);
      }

      return this.channels[queueName];
    } catch (error) {
      this.logger.error("Erro ao criar ou obter o canal:", error);
    }
  }

  // Método para enviar mensagens para uma fila específica
  async queueMessage<T extends string | object>(queueName: string, message: T): Promise<boolean> {
    let sended = false;
    try {
      const channel = await this.getChannel(queueName);
      if (channel) {
        await channel.assertQueue(queueName);

        // Converte a mensagem para Buffer dependendo do tipo
        const bufferMessage = typeof message === 'string'
          ? Buffer.from(message)
          : Buffer.from(JSON.stringify(message));

        // Envia a mensagem para a fila
        sended = channel.sendToQueue(queueName, bufferMessage, { persistent: true });
        this.logger.log(`Mensagem enviada para a fila ${queueName}: ${message}`);
      }
    } catch (error) {
      this.logger.error("Erro ao enviar mensagem:", error);
    } finally {
      return sended;
    }
  }


  async listenQueue<T extends string | object>(queueName: string, onMessageReceived: (message: T) => boolean): Promise<void> {
    try {
      // Conecta ao RabbitMQ e obtém o canal
      const channel = await this.getChannel(queueName);

      if (!channel) {
        this.logger.log("RABBITMQ X No Channel :");
        return;
      }

      // Define a fila a ser observada
      await channel.assertQueue(queueName);

      await channel.consume(queueName, (consumer) => {
        if (consumer) {
          const content = consumer.content.toString();

          // Tenta analisar o conteúdo como JSON para objetos
          let message: T;
          try {
            message = JSON.parse(content) as T;
          } catch {
            message = content as T; // Se não for JSON, trata como string
          }

          // Chama a função de callback com a mensagem
          const received = onMessageReceived(message);

          // Confirma que a mensagem foi recebida
          if (received) {
            channel.ack(consumer);
          }
        }
      });

    } catch (error) {
      this.logger.error("Erro ao escutar a fila:", error);
    }
  }


  // Método para fechar a conexão e os canais
  async close(): Promise<void> {
    try {
      for (const [queue, channel] of Object.entries(this.channels)) {
        await channel.close();
        this.logger.log(`Canal fechado para a fila: ${queue}`);
      }
      if (this.connection) {
        await this.connection.close();
        this.logger.log("Conexão com o RabbitMQ fechada");
      }
    } catch (error) {
      this.logger.error("Erro ao fechar a conexão/canais:", error);
    }
  }
}
