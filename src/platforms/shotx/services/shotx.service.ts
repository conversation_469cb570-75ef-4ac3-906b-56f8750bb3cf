import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { deleteKey } from '../../../libs/redis/redisClient'

export async function removeMessage(message: any, messageKey: string) {
  console.log('MESSAGE', message)
  console.log('MESSAGEKEY  > ID', message.id)

  try {
    // Use appointment_id directly from message if available, otherwise extract from messageId
    let appointmentId = message.appointment_id

    if (!appointmentId && message.id) {
      appointmentId = message.id.split('_')[0]
    }

    // Validate appointmentId
    if (!appointmentId || appointmentId.trim() === '') {
      throw new Error(
        'Invalid appointmentId: appointmentId is empty or undefined'
      )
    }

    console.log('APPOINTMENT_ID', appointmentId)

    const ref = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTXCRON)
      .doc(appointmentId)
      .collection('fails')
      .doc()

    const docToSave = {
      id: message.id,
      ...message,
    }

    console.log('DOCTOSAVE', docToSave)
    await ref.set(docToSave, { merge: true })
    await deleteKey(messageKey)

    console.log('Message successfully moved to fails collection')
  } catch (error) {
    console.error('Error in removeMessage:', error)
    throw error
  }
}
