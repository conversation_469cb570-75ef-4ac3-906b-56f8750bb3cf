import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { deleteKey } from '../../../libs/redis/redisClient'

export async function removeMessage(message: any, messageKey: string) {
  console.log('MESSAGE', message)
  console.log('MESSAGEKEY  > ID', message.id)

  try {
    // Try multiple possible fields for appointmentId
    let appointmentId = message.appointment_id || message.shotxCronId

    // If still no appointmentId, try extracting from messageId
    if (!appointmentId && message.id) {
      appointmentId = message.id.split('_')[0]
    }

    // Validate appointmentId
    if (!appointmentId || appointmentId.trim() === '') {
      console.error('Message object:', JSON.stringify(message, null, 2))
      throw new Error(
        'Invalid appointmentId: appointmentId is empty or undefined. Available fields: ' +
        Object.keys(message).join(', ')
      )
    }

    console.log('APPOINTMENT_ID', appointmentId)
    console.log('APPOINTMENT_ID type:', typeof appointmentId)
    console.log('APPOINTMENT_ID length:', appointmentId.length)
    console.log(
      'APPOINTMENT_ID chars:',
      appointmentId.split('').map((c: string) => c.charCodeAt(0))
    )

    // Sanitize appointmentId - remove any invalid characters
    const sanitizedAppointmentId = appointmentId.toString().trim()

    console.log('SANITIZED_APPOINTMENT_ID', sanitizedAppointmentId)

    const ref = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTXCRON)
      .doc(sanitizedAppointmentId)
      .collection('fails')
      .doc()

    const docToSave = {
      id: message.id,
      ...message,
    }

    console.log('DOCTOSAVE', docToSave)
    await ref.set(docToSave, { merge: true })
    await deleteKey(messageKey)

    console.log('Message successfully moved to fails collection')
  } catch (error) {
    console.error('Error in removeMessage:', error)
    throw error
  }
}
