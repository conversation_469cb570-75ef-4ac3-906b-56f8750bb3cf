import { Request, Response, Router } from 'express'
import { ShotxController } from '../controller/shotx.controller'

export const router = Router()

const RESOURCE = '/shotx'

router.post(
  `${RESOURCE}/broadcast/send`,
  async (req: Request, res: Response) => {
    const controller = new ShotxController()

    const { messages } = req.body
    console.log('MESSAGES', messages)

    controller.sendMessages(messages)

    res.send({
      error: false,
      //data: snipers,
    })
  }
)

export { router as shotxRouter }
