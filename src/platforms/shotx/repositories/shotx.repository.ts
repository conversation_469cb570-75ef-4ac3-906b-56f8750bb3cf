import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { instanceUpdateType } from '../../../libs/types/codechat/instanceUpdate'

export class ShotXRepository {
  collectionRef() {
    return firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
  }

  docRef(accountId: string) {
    return firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
  }

  all = async () => {
    return await this.collectionRef()
      .get()
      .then((snp) => snp.docs.map((doc) => doc.data()))
  }

  find = async (accountId: string) => {
    const shotx = this.docRef(accountId)

    return await shotx
      .get()
      .then((doc) => doc.data())
  }

  findInstance = async (accountId: string, instanceId: string) => {
    const shotx = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)

    return await shotx
      .get()
      .then((doc) => doc.data() as instanceUpdateType)
  }

  update = async (accountId: string, data: any) => {
    this.docRef(accountId).set(data, { merge: true })
  }

  override = async (accountId: string, data: any) => {
    this.docRef(accountId).set(data)
  }

}
