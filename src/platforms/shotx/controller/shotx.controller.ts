import { TRIGGERS } from '../../../constants/triggers'
import { InstagramController, WhatsappController } from '../../../controllers'
import { deleteKey, updateKeyInRedis } from '../../../libs/redis/redisClient'
import { saveShotxCronLogs } from '../../../libs/services/firestore/logs'
import { instanceUpdateType } from '../../../libs/types/codechat/instanceUpdate'
import { ShotXRepository } from '../repositories/shotx.repository'
import { removeMessage } from '../services/shotx.service'

export class ShotxController {
  private readonly repository: ShotXRepository

  public getApiKeys = async (accountId: string) => {
    const shotxDoc = await this.repository.find(accountId)

    return shotxDoc?.sniperTokens || []
  }

  constructor() {
    this.repository = new ShotXRepository()
  }

  public async sendMessages(messages: any[]) {
    const instances: instanceUpdateType[] = []
    for (const message of messages) {
      console.log('MEssage', message)
      let instance: any = instances.find(
        (instance) => message.instanceId === instance.id
      )

      if (!instance) {
        instance = await this.repository.findInstance(
          message.accountId,
          message.instanceId
        )

        if (!instance) {
          continue
        }
        instances.push(instance)
      }
      if (instance && instance.platform === 'Whatsapp') {
        const response: any = await WhatsappController.sendText(
          instance.ID,
          message.contact_remote_id,
          message.message.content,
          instance.userId,
          instance.accountId,
          true,
          { ...message.shotxCron, replied: false },
          message.contact_id
        )
        if (response.error) {
          console.log('WHATS ERROR')
          if (message?.attempts >= 3) {
            saveShotxCronLogs(
              message.appointment_id,
              instance.accountId,
              instance.ID,
              message.contact_id,
              TRIGGERS.ERROR,
              '',
              response
            )
            removeMessage(message, message.redis_key)
            return
          } else {
            const attempts = message?.attempts + 1
            await updateKeyInRedis(message.redis_key, 'attempts', attempts)
            return
          }
        }
        saveShotxCronLogs(
          message.shotxCronId,
          instance.accountId,
          instance.ID,
          message.contact_id,
          TRIGGERS.SEND_SUCCESS,
          '',
          response
        )
        deleteKey(message.redis_key)
      } else if (instance && instance.platform === 'Instagram') {
        const result = await InstagramController.sendMessage(
          message.contact_remote_id,
          message.message.content,
          instance.ID,
          { ...message.shotxCron, replied: false }
        )

        if (result.error) {
          console.log('ERROR', message?.attempts)
          if (message?.attempts >= 3) {
            console.log('ERROR INSIDE IF', message?.attempts)

            saveShotxCronLogs(
              message.shotxCronId,
              instance.accountId,
              instance.ID,
              message.contact_id,
              TRIGGERS.ERROR,
              '',
              result
            )
            removeMessage(message, message.redis_key)
          } else {
            const attempts = message?.attempts + 1
            await updateKeyInRedis(message.redis_key, 'attempts', attempts)
          }
        }
        saveShotxCronLogs(
          message.shotxCronId,
          instance.accountId,
          instance.ID,
          message.contact_id,
          TRIGGERS.SEND_SUCCESS,
          '',
          result
        )
        deleteKey(message.redis_key)
      }
    }
  }
}
