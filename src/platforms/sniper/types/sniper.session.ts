import { SniperSessionStatusTypes } from "../../../enums/sniper/SessionStatus"
import { SniperRef } from "./sniper.sniper"
import { SniperVariables } from "./sniper.variables"

export type SessionRef = {
  id: string
  sessionId: string
  resultId?: string
  accountId: string
  instanceId: string
  contactId: string
  platform: string
  status: SniperSessionStatusTypes
  sniper: SniperRef
  variables: SniperVariables
  leadId?: string
  startedAt: number
  closedAt: number
}