import { TRIGGERS } from '../../../constants/triggers'
import { InstagramController, WhatsappController } from '../../../controllers'
import LeadController from '../../../controllers/lead'
import {
  getMessageTypeEnum,
  MessageTypeEnum,
} from '../../../enums/core/MessageType'
import {
  getStatusFromString,
  SniperSessionStatusEnum,
} from '../../../enums/sniper/SessionStatus'
import { AccountRepository } from '../../../libs/repositories/firestore/account'
import { ShotXCronRepository } from '../../../libs/repositories/firestore/shotXCron'
import { saveShotxCronLogs } from '../../../libs/services/firestore/logs'
import { InstanceType } from '../../../libs/types/instance/instance'
import LeadType from '../../../libs/types/qiplus/lead'
import { getPhoneNumberInfo } from '../../../utils/phone.utils'
import { ShotxController } from '../../shotx/controller/shotx.controller'
import { SniperSessionRepository } from '../repositories/session.repository'
import { SniperRepository } from '../repositories/sniper.repository'
import { SniperService } from '../services/sniper.service'
import { SniperVariableAssociation } from '../types/sniper.association'
import { SniperResponse } from '../types/sniper.response'
import { SniperResult, VariableResult } from '../types/sniper.result'
import { SessionRef } from '../types/sniper.session'
import { SniperRef } from '../types/sniper.sniper'
import { SniperType } from '../types/sniper.type'
import { SniperVariables } from '../types/sniper.variables'

export class SniperController {
  private readonly service: SniperService
  private readonly sessionRepository: SniperSessionRepository
  private readonly sniperRepository: SniperRepository
  private readonly shotx: ShotxController
  public readonly supportedMessageTypes = [MessageTypeEnum.TEXT]
  constructor() {
    this.service = new SniperService()
    this.sessionRepository = new SniperSessionRepository()
    this.sniperRepository = new SniperRepository()
    this.shotx = new ShotxController()
  }

  public isSupportedMessageType(messageType: string) {
    const type = getMessageTypeEnum(messageType)
    if (!type) return false
    return this.supportedMessageTypes.includes(type)
  }

  public async messageReceived(
    instance: InstanceType,
    contactId: string,
    contactRemoteId: string,
    contactName: string,
    message: any,
    messageType: string,
    lead?: LeadType
  ) {
    const { id, accountId, sniperIds } = instance

    const result = {
      session: {} as SessionRef,
      messages: [] as SniperResponse[],
    }

    if (sniperIds && sniperIds.length > 0) {
      const isSupportedMessage = this.isSupportedMessageType(messageType)

      const session = await this.getSession(id, accountId, contactId)

      if (session) {
        result.session = session
        switch (session.status) {
          case SniperSessionStatusEnum.PAUSED:
            console.log('Paused Session')
            // Não fara nada
            return result
          case SniperSessionStatusEnum.OPENED:
            console.log('Opened Session -> Continue')
            if (isSupportedMessage) {
              result.messages =
                (await this.continue(session, message, lead)) || []
            } else {
              result.messages = [
                {
                  type: 'text',
                  message:
                    session.sniper.unknownMessage ||
                    'Tipo de mensagem não suportado',
                },
              ]
            }
            return result
        }
      }

      if (!isSupportedMessage) {
        console.log('Message not supported', { messageType, message })
        return result
      }

      // Variables to be sended to sniper
      const variables = {
        contactName,
        contactRemoteId,
        platform: instance.platform,
      } as SniperVariables

      // Verifica as triggers dos snipers
      const sniper = await this.getSniperMatch(sniperIds, message, accountId)

      if (sniper && sniper.publicSniper) {
        console.log('Session Not Found -> Start')
        result.messages =
          (await this.start(instance, sniper, contactId, variables)) || []
      }
    }

    return result
  }

  public async getSnipers(accountId: string) {
    return await this.sniperRepository.getAll(accountId)
  }

  public async getSniperMatch(
    sniperIds: string[],
    message: string,
    accountId: string
  ) {
    const snipers = await this.sniperRepository.getByIds(sniperIds, accountId)

    return snipers.find((sniper) => {
      if (sniper.triggerTypes === 'all') {
        return true
      }

      if (sniper.triggerTypes === 'keyword') {
        const { triggerOperator, triggerValue } = sniper

        // Verifica as diferentes condições de match
        switch (triggerOperator) {
          case 'contains':
            if (message.toLowerCase().includes(triggerValue.toLowerCase())) {
              console.log('contains match encontrado')
              return true // Encontrou o sniper que dá match
            }
            break
          case 'equals':
            if (message.toLowerCase() === triggerValue.toLowerCase()) {
              console.log('equals match encontrado')
              return true
            }
            break
          case 'startsWith':
            if (message.toLowerCase().startsWith(triggerValue.toLowerCase())) {
              console.log('startsWith match encontrado')
              return true
            }
            break
          case 'endsWith':
            if (message.toLowerCase().endsWith(triggerValue.toLowerCase())) {
              console.log('endsWith match encontrado')
              return true
            }
            break
          default:
            console.log('triggerOperator desconhecido')
            return false
        }
      }
      return false
    })
  }

  public async getSession(
    instanceId: string,
    accountId: string,
    contactId: string
  ) {
    // Busca todas as sessoes do contato
    const sessions = await this.sessionRepository.getByContactId(
      instanceId,
      accountId,
      contactId
    )

    // Caso encontre alguma sessão,
    if (sessions.length > 0) {
      const openedSessions = sessions.filter(
        (session) => session.status == SniperSessionStatusEnum.OPENED
      )
      if (openedSessions.length > 0) {
        return openedSessions[0]
      }

      const pausedSessions = sessions.filter(
        (session) => session.status == SniperSessionStatusEnum.PAUSED
      )
      if (pausedSessions.length > 0) {
        return pausedSessions[0]
      }
    }

    return null
  }

  public async getSessionsOpened(
    instanceId: string,
    accountId: string,
    contactId: string
  ) {
    // Busca todas as sessoes do contato
    return await this.sessionRepository.getAllContactSessionsOpened(
      instanceId,
      accountId,
      contactId
    )
  }

  public async getAnotherOpenedSessions(
    instanceId: string,
    accountId: string,
    contactId: string,
    sessionId: string
  ) {
    // Busca as sessoes ativas
    return await this.sessionRepository.getAllByContactIdAndOpened(
      instanceId,
      accountId,
      contactId,
      sessionId
    )
  }

  public async start(
    instance: InstanceType,
    sniperDoc: SniperRef,
    contactId: string,
    variables: SniperVariables
  ) {
    const { accountId, id, platform } = instance
    const startResponse = await this.service.startSniper(
      sniperDoc.publicSniper,
      variables
    )
    if (startResponse.status != 200) {
      console.log(
        'Start session failed',
        startResponse.data?.data || startResponse.data
      )
      return null
    } else {
      const { sessionId, resultId, input, sniper } = startResponse.data
      sniperDoc.privateId = sniper.id

      const session = {
        sessionId,
        accountId,
        contactId,
        instanceId: id,
        platform,
        sniper: sniperDoc,
        status: input
          ? SniperSessionStatusEnum.OPENED
          : SniperSessionStatusEnum.CLOSED,
        variables,
      } as SessionRef

      console.log('session', session)

      if (resultId) {
        session.resultId = resultId
      }

      this.sessionRepository.create(session)

      return this.processMessages(startResponse, sniper.unknownMessage)
    }
  }

  public async continue(session: SessionRef, message: string, lead?: LeadType) {
    const { sessionId, sniper } = session

    if (sniper?.keywordFinish?.toLowerCase() === message) {
      this.sessionRepository.close(session)
      // this.onSessionClose(session) // Descomentar para que o lead seja atualizado tbm ao sair do fluxo
      return null
    }

    // Chamar o sniper pelo sniperId da sessao para continuar a conversa
    const response = await this.service.continueSniper(sessionId, message)
    const { input } = response.data

    if (!input || response.status != 200) {
      this.sessionRepository.close(session)
      this.onSessionClose(session, lead)
    }

    if (response.status != 200) {
      console.log('Continue session failed', response.data)
      return null
    }

    return this.processMessages(response, sniper?.unknownMessage)
  }

  public async onSessionClose(session: SessionRef, lead?: LeadType) {
    const { resultId, sniper, platform } = session
    const leadVarsAssociation = sniper.leadVarsAssociation || []

    if (sniper.updateLead && leadVarsAssociation.length > 0 && resultId) {
      // Busca o resultado do fluxo
      const resultResponse = await this.service.getResults(
        sniper.privateId,
        resultId,
        sniper.apiKey
      )
      const result = resultResponse.data?.result
      if (!result) return

      // Busca o sniper
      const sniperResponse = await this.service.getSniper(
        sniper.privateId,
        sniper.apiKey
      )
      const sniper_schema = sniperResponse.data?.sniper
      if (!sniper_schema) return

      // Obtem uma lista com as variáveis retornadas do fluxo
      let variablesResult = this.getVariablesList(result, sniper_schema)

      // Pega os valores default para os campos de nome (os mesmos valores enviados nas variaveis)
      const defaultValues = this.getDefaultValuesToFields(
        session.variables,
        leadVarsAssociation
      )
      variablesResult = [
        ...variablesResult,
        ...defaultValues.filter(
          (field) => !variablesResult.some((res) => res.name === field.name)
        ),
      ]

      // Obtem um objeto com os campos a atualizar no lead
      let matchedVars = this.getMatchedQiplusVars(
        variablesResult,
        leadVarsAssociation
      )

      // Prepara os dados para o padrão do sistema
      matchedVars = LeadController.sanitizeMatchedVarsToUpdateLead(matchedVars)

      if (Object.keys(matchedVars).length > 0) {
        switch (platform.toLowerCase()) {
          case 'whatsapp':
            WhatsappController.onSniperSessionClose(session, matchedVars, lead)
            break
          case 'instagram':
            InstagramController.onSniperSessionClose(session, matchedVars)
            break
          default:
            break
        }
      }
    }
  }

  public getVariablesList = (
    result: SniperResult,
    sniper: SniperType
  ): VariableResult[] => {
    if (!result || !sniper || !sniper.groups || !sniper.variables) {
      return []
    }

    const variables: VariableResult[] = []

    // Convertendo o array de answers em um Map para busca rápida
    const answersMap = new Map(
      result.answers.map((ans) => [ans.blockId, ans.content])
    )

    // Convertendo o array de variables em um Map para busca rápida
    const variablesMap = new Map(sniper.variables.map((v) => [v.id, v.name]))

    // Iterando pelos grupos e blocos
    sniper.groups.flatMap((group) =>
      group.blocks.map((block) => {
        const value = answersMap.get(block.id) // Busca rápida pelo blockId
        const name = variablesMap.get(block.options?.variableId) // Busca rápida pelo variableId

        if (name && value) {
          variables.push({
            name,
            value,
          })
        }
      })
    )

    return variables
  }

  public getMatchedQiplusVars = (
    variablesResult: VariableResult[],
    leadVarsAssociation: SniperVariableAssociation[]
  ) => {
    const matched: any = {}
    leadVarsAssociation.forEach(({ qiplus, sniper }) => {
      const value = variablesResult.find((res) => sniper === res.name)?.value
      if (value) matched[qiplus] = value
    })
    return matched as object
  }

  // Pega das variaveis enviada no inicio do fluxo
  // Quando o campo a associar/atualizar for dos tipos (nome, telefone)
  // Esses dados serão usados para mesclar com o resultado do fluxo
  // ISSO É NECESSARIO POIS O FLUXO NÃO DEVOLVE AS VARIAVEIS PASSADAS NO INICIO
  // A MENOS QUE ELAS TENHAS SIDO ALTERADAS DURANTE O FLUXO
  public getDefaultValuesToFields = (
    defaultVariables: SniperVariables,
    associations: SniperVariableAssociation[]
  ) => {
    // Verifica se o campo é um campo de nome
    const isQIPLUSNameField = (field: string) =>
      LeadController.nameFields.includes(field)
    const isQIPLUSPhoneField = (field: string) =>
      LeadController.phoneFields.includes(field)

    const defaultValues: VariableResult[] = []
    associations.forEach(({ qiplus, sniper }) => {
      if (isQIPLUSNameField(qiplus)) {
        defaultValues.push({
          name: sniper,
          value: defaultVariables.contactName,
        })
      }
      if (isQIPLUSPhoneField(qiplus)) {
        defaultValues.push({
          name: sniper,
          value: defaultVariables.contactRemoteId,
        })
      }
    })
    return defaultValues
  }

  public applyFormatting(element: any) {
    let text = ''

    if (element.text) {
      text += element.text
    }

    if (element.children && element.type !== 'a') {
      for (const child of element.children) {
        text += this.applyFormatting(child)
      }
    }

    if (element.type === 'p' && element.type !== 'inline-variable') {
      text = text.trim() + '\n'
    }

    if (element.type === 'inline-variable') {
      text = text.trim()
    }

    if (element.type === 'ol') {
      text =
        '\n' +
        text
          .split('\n')
          .map((line, index) => (line ? `${index + 1}. ${line}` : ''))
          .join('\n')
    }

    if (element.type === 'li') {
      text = text
        .split('\n')
        .map((line) => (line ? `  ${line}` : ''))
        .join('\n')
    }

    let formats = ''

    if (element.bold) {
      formats += '*'
    }

    if (element.italic) {
      formats += '_'
    }

    if (element.underline) {
      formats += '~'
    }

    let formattedText = `${formats}${text}${formats.split('').reverse().join('')}`

    if (element.url) {
      formattedText = element.children[0]?.text
        ? `[${formattedText}]\n(${element.url})`
        : `${element.url}`
    }

    return formattedText
  }

  public processMessages(sniperResponse: any, unknownMessage: string) {
    const { messages, input } = sniperResponse.data
    let responses = [] as SniperResponse[]

    for (const message of messages) {
      const response = {
        type: message.type,
      } as SniperResponse

      switch (message.type) {
        case 'text':
          let formattedText = ''

          for (const richText of message.content.richText) {
            for (const element of richText.children) {
              formattedText += this.applyFormatting(element)
            }
            formattedText += '\n'
          }

          formattedText = formattedText
            .replace(/\*\*/g, '')
            .replace(/__/, '')
            .replace(/~~/, '')
            .replace(/\n$/, '')

          formattedText = formattedText.replace(/\n$/, '')

          if (
            formattedText.toLowerCase() ===
            'Invalid message. Please, try again.'.toLowerCase()
          ) {
            response.message = unknownMessage
          } else {
            response.message = formattedText
          }
          responses.push(response)
          break
        case 'image':
        case 'video':
        case 'audio':
          response.message = message.content.url
          responses.push(response)
          break
        default:
          break
      }
    }
    if (input) {
      responses = responses.concat(this.processInput(input))
    }

    return responses
  }

  public processInput(input: any) {
    const responses = []
    if (input.type === 'choice input') {
      const items = input.items || []
      const buttons = items.map((item: any) => {
        return {
          type: 'postback',
          title: item.content,
          payload: item.content,
        }
      })

      const response = {
        type: 'button',
        input: true,
        text: false,
        image: false,
        video: false,
        audio: false,
        message: buttons,
      } as SniperResponse
      responses.push(response)
    }
    return responses
  }

  public async setSessionStatus(
    instanceId: string,
    accountId: string,
    contactId: string,
    status: string
  ) {
    const newStatus = getStatusFromString(status)
    const session = await this.getSession(instanceId, accountId, contactId)

    if (session && newStatus && newStatus != session.status) {
      if (
        newStatus == SniperSessionStatusEnum.CLOSED ||
        newStatus == SniperSessionStatusEnum.PAUSED
      ) {
        const sessionsOpened = await this.getAnotherOpenedSessions(
          instanceId,
          accountId,
          contactId,
          session.sessionId
        )
        if (sessionsOpened.length > 0) {
          sessionsOpened.forEach((sessionOpened) => {
            this.sessionRepository.update(
              sessionOpened,
              SniperSessionStatusEnum.CLOSED
            )
          })
        }
      }
      return this.sessionRepository
        .update(session, newStatus)
        .then((result) => {
          return {
            error: false,
            data: result,
          }
        })
        .catch((error) => {
          return {
            error: true,
            data: error,
          }
        })
    } else {
      return {
        error: false,
        data: {
          message: 'no session to update',
        },
      }
    }
  }

  public async initSniperManually(
    instance: InstanceType,
    sniper: SniperRef,
    contactId: string,
    contactRemoteId: string,
    contactName: string,
    shotxCron?: any
  ) {
    const {
      platform,
      auth: { longToken },
    } = instance

    if (instance.platform == 'Whatsapp') {
      const phone = getPhoneNumberInfo(contactRemoteId, true)
      contactRemoteId = phone?.international || contactRemoteId
      contactId = phone?.international || contactRemoteId
    }

    const variables = {
      platform,
      contactRemoteId,
      contactName,
    } as SniperVariables

    const { id, accountId } = instance
    const sessions = await this.getSessionsOpened(id, accountId, contactId)

    const responses = await this.start(instance, sniper, contactId, variables)

    if (!responses) {
      return {
        error: false,
        message: 'Sniper responses empty',
      }
    }

    // Se deu certo de iniciar, encerra as anteriores
    if (sessions.length > 0) {
      for (const session of sessions) {
        this.sessionRepository.update(session, SniperSessionStatusEnum.CLOSED)
      }
    }
    let payload

    switch (platform) {
      case 'instagram':
      case 'Instagram':
        payload = await InstagramController.sendReply(
          contactRemoteId,
          longToken,
          responses
        )
        break
      case 'whatsapp':
      case 'Whatsapp':
        payload = await WhatsappController.sendReply(
          instance,
          contactRemoteId,
          responses,
          shotxCron
        )
        break
    }

    if (payload.error) {
      return {
        error: true,
        message: 'Sniper initialize',
        payload: payload,
      }
    } else {
      return {
        error: false,
        message: 'Sniper initialize',
        payload: payload,
      }
    }
  }

  async adjustSnipersKeys() {
    const accounts = await new AccountRepository().all()
    const result = {
      accounts: accounts.length,
      snipers: 0,
    }
    for (const [index, account] of accounts.entries()) {
      const snipers = (await this.sniperRepository.getAll(account.id)) as any[]
      result.snipers += snipers.length
      if (snipers.length === 0) {
        console.log(
          `Skipping account ${index + 1} de ${accounts.length} : ${account.id} (${snipers.length} snipers)`
        )
        continue
      }

      console.log(
        `Account ${index + 1} de ${accounts.length} : ${account.id} (${snipers.length} snipers)`
      )

      for (const sniper of snipers) {
        console.log(
          `Migrando Snipers ${index + 1} de ${snipers.length} - accountId: ${account}`
        )
        let put = false
        if (sniper.customFields) {
          sniper.leadVarsAssociation = sniper.customFields
          delete sniper['customFields']
          put = true
        }
        if (sniper.api_key) {
          sniper.apiKey = sniper.api_key
          delete sniper['api_key']
          put = true
        }
        if (sniper.public_sniper) {
          sniper.publicSniper = sniper.public_sniper
          delete sniper['public_sniper']
          put = true
        }

        if (put) {
          console.log(`Updating sniper`, sniper)
          this.sniperRepository.override(sniper)
        }
      }
    }

    return result
  }

  async sendBroadcast(shotXCron: any) {
    const shotXCronRepository = new ShotXCronRepository()
    const { sniper, instance, id, contacts } = shotXCron
    shotXCronRepository.update(id, {
      executing: true,
    })

    for (const contact of contacts) {
      if (!instance || !contact.contactRemoteId || !contact.contactName) {
        saveShotxCronLogs(
          id,
          instance.accountId,
          instance.id,
          contact.id,
          TRIGGERS.ERROR,
          '',
          {
            error: true,
            data: 'instanceId and contact',
          }
        )
      } else {
        const result = await this.initSniperManually(
          instance,
          sniper,
          contact.contactRemoteId,
          contact.contactRemoteId,
          contact.contactName,
          {
            id: id,
            contactId: contact.id,
            replied: false,
          }
        )

        const message =
          result?.payload?.data.key?.id ||
          result?.payload?.data.message?.message_id ||
          null

        if (result.error) {
          saveShotxCronLogs(
            id,
            instance.accountId,
            instance.id,
            contact.id,
            TRIGGERS.ERROR,
            message,
            result
          )
        } else {
          saveShotxCronLogs(
            id,
            instance.accountId,
            instance.id,
            contact.id,
            TRIGGERS.SEND_SUCCESS,
            message,
            result
          )
        }
      }
    }

    shotXCronRepository.update(id, {
      executing: false,
      executed: true,
    })
  }

  async getSniperAccountData(accountId: string) {
    const credentials = await this.shotx.getApiKeys(accountId)
    const data: any[] = []
    if (credentials.length <= 0) {
      return 'Nenhuma API Key encontrada'
    }

    for (const credential of credentials) {
      const response = await this.service.getWorkspaces(credential.token)
      const workspaces = response.data.workspaces || []

      for (const [index, workspace] of workspaces.entries()) {
        const response = await this.service.getSnipers(
          workspace.id,
          credential.token
        )

        workspace.snipers = response.data.snipers || []
        workspaces[index] = workspace
      }
      credential.workspaces = workspaces
      data.push(credential)
    }

    return data
  }

  async getSniper(privateSniperId: string, apiKey: string) {
    const sniper = await this.service.getSniper(privateSniperId, apiKey)
    return sniper.data
  }
}
