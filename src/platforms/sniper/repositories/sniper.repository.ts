import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { SniperRef } from '../types/sniper.sniper'

export class SniperRepository {
  getSniperRef(accountId: string) {
    return firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.SNIPERS)
  }

  all = async (): Promise<SniperRef[]> => {
    return await firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .get()
      .then((snp) => snp.docs.map((doc) => doc.data() as SniperRef))
  }

  getAll = async (accountId: string): Promise<SniperRef[]> => {
    const snipers = this.getSniperRef(accountId)

    return await snipers
      .get()
      .then((snp) => snp.docs.map((doc) => doc.data() as SniperRef))
  }

  getByIds = async (ids: string[], accountId: string): Promise<SniperRef[]> => {
    const snipers = this.getSniperRef(accountId).where('id', 'in', ids)

    return await snipers
      .get()
      .then((snp) => snp.docs.map((doc) => doc.data() as SniperRef))
  }

  update = async (sniper: SniperRef) => {
    const { id, accountId } = sniper
    this.getSniperRef(accountId).doc(id).set(sniper, { merge: true })
  }

  override = async (sniper: SniperRef) => {
    const { id, accountId } = sniper
    this.getSniperRef(accountId).doc(id).set(sniper)
  }

}
