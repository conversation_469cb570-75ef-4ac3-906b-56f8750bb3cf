import { NextFunction, Request, Response } from 'express';
type FieldsSchema = {
  [key: string]: any;
};

const requiredBodyFields =
{
  'instance': {
    'accountId': '',
    'id': '',
    'platform': '',
  },
  'sniper': {
    'accountId': '',
    'publicSniper': '',
  },
  'contactRemoteId': '',
  'contactName': '',
}

const requiredParams = [
  'contactId'
]

const getInvalidFields = (obj: object, fields: FieldsSchema): string[] => {
  return Object.entries(fields)
    .filter(([field, value]) => {
      // Verifica se o campo não existe no objeto
      if (!Object.hasOwn(obj, field)) return true;

      // Se o valor esperado é um objeto, realiza a validação recursiva
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        const subObj = (obj as any)[field];

        // Se o subObj não é um objeto, considera inválido
        if (typeof subObj !== 'object' || subObj === null || Array.isArray(subObj)) {
          return true;
        }

        // Chamada recursiva para validar campos aninhados
        return getInvalidFields(subObj, value).length > 0;
      }

      // Se o valor não é um objeto e o campo existe, considera válido
      return false;
    })
    .map(([field]) => field);
};

export const SniperInitMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {

  const invalidParams = requiredParams.filter((p) => !req.params[p])
  if (invalidParams.length) {
    return res.status(400).send({
      error: true,
      data: {
        message: `params ${invalidParams.join(',')} are required`,
      },
    })
  }

  let invalidFields = getInvalidFields(req.body, requiredBodyFields)
  if (invalidFields?.length) {
    return res.status(400).send({
      error: true,
      data: {
        message: `${invalidFields.join(',')} not found or is incorrect`,
      },
    })
  }

  next()
}
