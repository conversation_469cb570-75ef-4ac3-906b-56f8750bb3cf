import { NextFunction, Request, Response } from 'express'

const requiredBodyFields = [
  'instance',
  'sniper',
  'contactRemoteId',
  'contactName',
]

const requiredParams = [
  'contactId'
]

export const SniperSessionUpdateMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {

  for (const param of requiredParams) {
    if (!req.params[param]) {
      return res.status(400).send({
        error: true,
        data: {
          message: `param ${param} are required`,
        },
      })
    }
  }

  for (const field of requiredBodyFields) {
    if (!req.body[field]) {
      return res.status(400).send({
        error: true,
        data: {
          message: `${field} are required`,
        },
      })
    }
  }

  next()
}
