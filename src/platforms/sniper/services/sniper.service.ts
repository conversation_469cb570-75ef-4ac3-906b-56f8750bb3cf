import axios, { AxiosInstance } from 'axios'
import { SniperVariables } from '../types/sniper.variables'

export class SniperService {
  private api: AxiosInstance
  private builderApi: AxiosInstance

  constructor() {
    const baseURL = process.env?.SNIPER_URL
    const builderBaseUrl = process.env?.SNIPER_BUILDER_URL

    if (!baseURL || !builderBaseUrl) {
      throw new Error('SNIPER_URL ou SNIPER_BUILDER_URL não informada no .env')
    }

    this.api = axios.create({
      baseURL: baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.builderApi = axios.create({
      baseURL: builderBaseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  apiWithParams(
    params: any,
    instance?: AxiosInstance,
    authorization?: string
  ): AxiosInstance {
    const api = instance || this.api

    if (authorization) {
      api.defaults.headers.put.Authorization = authorization
    }

    if (params) {
      api.defaults.params = params
    }
    return api
  }

  public async startSniper(sniperId: string, variables: SniperVariables) {
    const url = `/api/v1/snipers/${sniperId}/startChat`
    const body = {
      prefilledVariables: variables,
    }

    return this.api.post(url, body).catch((reason) => {
      return {
        error: true,
        status: 404,
        data: reason,
      }
    })
  }

  public async continueSniper(sessionId: string, message: string) {
    const body = {
      message,
    }
    return this.api
      .post(`/api/v1/sessions/${sessionId}/continueChat`, body)
      .catch((reason) => {
        return {
          error: false,
          status: 404,
          data: reason,
        }
      })
  }

  public async getResults(
    privateSniperId: string,
    resultId: string,
    apiKey: string
  ) {
    const options = {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    }

    return this.builderApi
      .get(`/api/v1/snipers/${privateSniperId}/results/${resultId}`, options)
      .catch((reason) => {
        return {
          error: false,
          status: 404,
          data: reason,
        }
      })
  }

  public async getSniper(privateSniperId: string, apiKey: string) {
    const options = {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    }

    return this.builderApi
      .get(`/api/v1/snipers/${privateSniperId}`, options)
      .catch((reason) => {
        return {
          error: false,
          status: 404,
          data: reason,
        }
      })
  }

  public async getSnipers(workspaceId: string, apiKey: string) {
    const options = {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
      params: {
        workspaceId,
      },
    }

    return this.builderApi.get(`/api/v1/snipers`, options).catch((reason) => {
      return {
        error: false,
        status: 404,
        data: reason,
      }
    })
  }
  public async getWorkspaces(apiKey: string) {
    const options = {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    }

    return this.builderApi
      .get(`/api/v1/workspaces`, options)
      .catch((reason) => {
        return {
          error: false,
          status: 404,
          data: reason,
        }
      })
  }
}
