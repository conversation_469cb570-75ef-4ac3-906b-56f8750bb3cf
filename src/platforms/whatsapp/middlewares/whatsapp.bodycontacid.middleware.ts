import { NextFunction, Request, Response } from 'express';
import { getPhoneNumberInfo } from '../../../utils/phone.utils';

export const ContactIdBodyRequired = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    console.log(`🚀 ~ 💬 WHATSAPP BROADCAST `, req.body);

    const { contactId } = req.body

    // Processar e obter informações sobre o número de telefone
    if (contactId) {

      const phoneInfo = getPhoneNumberInfo(contactId, true);

      if (phoneInfo) {

        const { national, international, countryCode } = phoneInfo;

        // Inserir os dados processados no corpo da requisição
        req.body.phone = national;
        req.body.phoneCC = countryCode;
        req.body.phoneNumber = international;

        // Atualizar o contactId com o número corrigido (9º dígito)
        req.body.contactid = international
      }
    }

    console.log(`🚀 ~ 💬 BODY`, JSON.stringify(req.body));
    // Continuar para o próximo middleware ou rota
    next();
  } catch (error: any) {
    console.error(`Error in ContactIdRequired: ${error.message}`, { body: req.body });
    res.status(500).send({
      error: true,
      message: 'Internal Server Error',
    });
  }
};
