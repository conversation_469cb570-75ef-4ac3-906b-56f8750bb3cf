import moment from 'moment'
import { instanceCreateType } from '../types/codechat/instanceCreate'

export const instanceModel = {
  ID: '',
  title: '',
  platform: '',
  accountId: '',
  userId: '',
  status: {
    state: 'close',
    statusReason: 200,
    message: '',
    owner: '',
    profile: '',
  },
  auth: {
    jwt: '',
    longToken: '',
  },
  subscriptions: [] as string[],
  createdAt: moment().unix(),
  updatedAt: moment().unix(),
} as instanceCreateType
