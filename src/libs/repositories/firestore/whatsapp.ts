import moment from 'moment'
import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { TRIGGERS } from '../../../constants/triggers'
import { MessageTypeEnum } from '../../../enums/codechat/MessageType'
import { EvolutionMessageStatusEnum } from '../../../enums/evolution/MessageStatus'
import { dateFromNum, dateToString } from '../../../utils/dateUtils'
import { saveShotxCronLogs } from '../../services/firestore/logs'
import { instanceCreateType } from '../../types/codechat/instanceCreate'
import { instanceStatusDto } from '../../types/codechat/instanceStatus'
import {
  MessageMediaResponse,
  MessageUpSertData,
} from '../../types/evolution/message/messageUpsert'

export class WhatsappRepository {
  onTextMessage = async (
    instanceId: string,
    accountId: string,
    messageData: any
  ) => {
    const { timestamp, date, read, fromMe, phoneNumber, messageId } =
      this.getMessageData(messageData)

    const message = messageData.message.text

    const messageDoc = {
      id: messageId,
      message,
      receiver: !fromMe,
      sender: fromMe,
      read,
      timestamp,
      codechat: messageData,
      date,
      instanceId,
      accountId,
      type: MessageTypeEnum.TEXT,
    }
    this.saveMessage(accountId, instanceId, phoneNumber, messageId, messageDoc)
    this.saveCount(accountId, instanceId, phoneNumber)
  }

  onExtendedTextMessage = async (
    instanceId: string,
    accountId: string,
    messageData: any
  ) => {
    const { timestamp, date, read, fromMe, phoneNumber, messageId } =
      this.getMessageData(messageData)

    const message =
      messageData.message.extendedTextMessage?.text ||
      messageData.message.conversation
    if (!message) {
      console.log(
        'FALHA AO CAPTURAR MENSAGEM > onExtendedTextMessage',
        messageData
      )
      return
    }

    const messageDoc = {
      id: messageId,
      message,
      receiver: !fromMe,
      sender: fromMe,
      read,
      timestamp,
      codechat: messageData,
      date,
      instanceId,
      accountId,
      type: MessageTypeEnum.TEXT,
    }
    this.saveMessage(
      accountId,
      instanceId,
      phoneNumber,
      messageId,
      messageDoc,
      true
    )
    this.saveCount(accountId, instanceId, phoneNumber)
  }

  onConversationMessage = async (
    instanceId: string,
    accountId: string,
    phoneNumber: string,
    messageData: any
  ) => {
    const { message } = messageData
    const { timestamp, date, read, fromMe, messageId } =
      this.getMessageData(messageData)

    const messageDoc = {
      id: messageId,
      message: message.conversation,
      receiver: !fromMe,
      sender: fromMe,
      read,
      timestamp,
      codechat: messageData,
      date,
      instanceId,
      accountId,
      type: MessageTypeEnum.TEXT,
    }

    this.saveMessage(
      accountId,
      instanceId,
      phoneNumber,
      messageId,
      messageDoc,
      true
    )
    this.saveCount(accountId, instanceId, phoneNumber)
  }

  onImageMessage = async (
    instanceId: string,
    accountId: string,
    phoneNumber: string,
    messageData: MessageUpSertData
  ) => {
    const { key } = messageData

    const { timestamp, date, read } = this.getMessageData(messageData)
    const messageId = key.id

    const messageDoc = {
      id: messageId,
      image: messageData.message,
      receiver: !key.fromMe,
      sender: key.fromMe,
      read,
      timestamp,
      codechat: messageData,
      date,
      instanceId,
      accountId,
      type: MessageTypeEnum.IMAGE,
    }
    this.saveMessage(accountId, instanceId, phoneNumber, messageId, messageDoc)
    this.saveCount(accountId, instanceId, phoneNumber)
  }

  onVideoMessage = async (
    instanceId: string,
    accountId: string,
    phoneNumber: string,
    messageData: MessageUpSertData
  ) => {
    const { timestamp, date, read } = this.getMessageData(messageData)
    const { key } = messageData
    const messageId = key.id

    const messageDoc = {
      id: messageId,
      video: messageData.message,
      receiver: !key.fromMe,
      sender: key.fromMe,
      read,
      timestamp,
      codechat: messageData,
      date,
      instanceId,
      accountId,
      type: MessageTypeEnum.VIDEO,
    }
    this.saveMessage(accountId, instanceId, phoneNumber, messageId, messageDoc)
    this.saveCount(accountId, instanceId, phoneNumber)
  }

  onDocumentMessage = async (
    instanceId: string,
    accountId: string,
    phoneNumber: string,
    messageData: MessageUpSertData
  ) => {
    const { timestamp, date, read } = this.getMessageData(messageData)
    const { key } = messageData
    const messageId = key.id

    const messageDoc = {
      id: messageId,
      file: messageData.message,
      receiver: !key.fromMe,
      sender: key.fromMe,
      read,
      timestamp,
      codechat: messageData,
      date,
      instanceId,
      accountId,
      type: MessageTypeEnum.FILE,
    }
    this.saveMessage(accountId, instanceId, phoneNumber, messageId, messageDoc)
    this.saveCount(accountId, instanceId, phoneNumber)
  }

  onAudioMessage = async (
    instanceId: string,
    accountId: string,
    phoneNumber: string,
    messageData: MessageUpSertData
  ) => {
    const { timestamp, date, read } = this.getMessageData(messageData)
    const { key } = messageData
    const messageId = key.id

    const messageDoc = {
      id: messageId,
      audio: messageData.message,
      receiver: !key.fromMe,
      sender: key.fromMe,
      read,
      timestamp,
      codechat: messageData,
      date,
      instanceId,
      accountId,
      type: MessageTypeEnum.AUDIO,
    }
    this.saveMessage(accountId, instanceId, phoneNumber, messageId, messageDoc)
    this.saveCount(accountId, instanceId, phoneNumber)
  }

  saveMessageSent = async (
    instanceId: string,
    accountId: string,
    userId: string,
    whatsappId: string,
    errorSend: boolean,
    messageData: any,
    shotxCron?: object
  ) => {
    const messageId = messageData?.key?.id
    const phoneNumber = this.getPhoneNumber(whatsappId)
    const timestamp = Number(messageData.messageTimestamp)
    const date = this.getDate(timestamp)

    const message =
      messageData?.message?.extendedTextMessage?.text ||
      messageData?.message?.conversation
    let messageNewDoc: any = {
      id: messageId,
      message,
      receiver: false,
      sender: true,
      errorSend,
      read: false,
      userId,
      accountId,
      instanceId,
      timestamp,
      codechat: messageData,
      date,
    }

    if (shotxCron) {
      messageNewDoc = {
        ...messageNewDoc,
        shotxCron,
      }
    }

    console.log('🗝️ Save message sent', messageNewDoc, 'messageID', messageId)
    this.saveMessage(
      accountId,
      instanceId,
      phoneNumber,
      messageId,
      messageNewDoc
    )
  }

  saveMessageMedia = async (
    instanceId: string,
    accountId: string,
    userId: string,
    whatsappId: string,
    sendResultData: MessageMediaResponse,
    mediaType: string,
    mediaUrl: string
  ) => {
    const messageId = sendResultData.key.id
    const phoneNumber = this.getPhoneNumber(whatsappId)
    const timestamp = Number(sendResultData.messageTimestamp)
    const date = this.getDate(timestamp)

    const message = {
      id: messageId,
      receiver: false,
      sender: true,
      errorSend: false,
      read: false,
      userId,
      accountId,
      instanceId,
      timestamp,
      codechat: sendResultData,
      date,
      mediaUrl,
      type: mediaType,
      audio: {},
      image: {},
      file: {},
      video: {},
    }

    switch (mediaType) {
      case 'audio':
        message.audio = {
          ...sendResultData.message.audioMessage,
          mediaUrl,
        }
        break
      case 'image':
        message.image = {
          ...sendResultData.message.imageMessage,
          mediaUrl,
        }
        break
      case 'video':
        message.video = {
          ...sendResultData.message.videoMessage,
          mediaUrl,
        }
        break
      case 'file':
        message.file = {
          documentMessage: sendResultData.message.documentMessage,
          mediaUrl,
        }
        break
      case 'document':
        message.file = {
          documentMessage: sendResultData.message.documentMessage,
          mediaUrl,
        }
        message.type = 'file'
        break
      default:
        console.log(`${mediaType} | MEDIA TYPE NOT IMPLEMENTED`)
        break
    }

    this.saveMessage(accountId, instanceId, phoneNumber, messageId, message)
  }

  saveFailMessageSent = async (
    instanceId: string,
    accountId: string,
    userId: string,
    phoneNumber: string,
    message: string,
    messageData: any
  ) => {
    const timestamp = moment().unix()
    const date = moment().format('YYYYMMDD')

    const messageNewDoc = {
      message,
      receiver: false,
      sender: true,
      errorSend: true,
      read: false,
      userId,
      accountId,
      instanceId,
      timestamp,
      codechat: messageData,
      date,
    }

    this.saveFailMessage(accountId, instanceId, phoneNumber, messageNewDoc)
  }

  saveNewMessage = async (
    instanceId: string,
    accountId: string,
    phoneNumber: string,
    messageData: MessageUpSertData
  ) => {
    const messageType = messageData.messageType

    switch (messageType) {
      case 'extendedTextMessage': //extendedTextMessage
        this.onExtendedTextMessage(instanceId, accountId, messageData)
        break
      case 'conversation':
        this.onConversationMessage(
          instanceId,
          accountId,
          phoneNumber,
          messageData
        )
        break
      case 'imageMessage':
        this.onImageMessage(instanceId, accountId, phoneNumber, messageData)
        break
      case 'videoMessage':
        this.onVideoMessage(instanceId, accountId, phoneNumber, messageData)
        break
      case 'audioMessage':
        this.onAudioMessage(instanceId, accountId, phoneNumber, messageData)
        break
      case 'documentMessage':
        this.onDocumentMessage(instanceId, accountId, phoneNumber, messageData)
        break
      default:
        console.log(`🗝️messageType | not implemented`)
        break
    }

    const { fromMe } = this.getMessageData(messageData)

    return { fromMe, phoneNumber }
  }

  saveEditedMessage = async (
    instanceId: string,
    accountId: string,
    phoneNumber: string,
    messageData: any
  ) => {
    const { read, fromMe } = this.getMessageData(messageData)

    const message =
      messageData?.message?.editedMessage?.message?.protocolMessage
        ?.editedMessage?.conversation
    const messageId =
      messageData?.message?.editedMessage?.message?.protocolMessage?.key?.id

    if (!message || !messageId) {
      console.log(
        'FALHA AO CAPTURAR DADOS DA MENSAGEM EDITADA > saveEditedMessage',
        { message, messageId }
      )
      return
    }

    const updateDoc = {
      codechat: messageData,
      message,
      edited: true,
      receiver: !fromMe,
      sender: fromMe,
      read,
    }

    this.saveMessage(
      accountId,
      instanceId,
      phoneNumber,
      messageId,
      updateDoc,
      true
    )
  }

  updateMessage = async (
    instanceId: string,
    accountId: string,
    messageData: any,
    { deleted = false, read = false }
  ) => {
    // Precisa pegar daqui pois não é enviado no front para marcar como lidas
    const { messageId, phoneNumber } = this.getMessageData(messageData)

    if (!accountId || !instanceId || !phoneNumber || !messageId) {
      console.log(
        'updateMessage !accountId || !instanceId || !phoneNumber || !messageId'
      )
      return
    }
    const docRef = this.getMessageDocReference(
      accountId,
      instanceId,
      phoneNumber,
      messageId
    )

    const { status = EvolutionMessageStatusEnum.READ } = messageData
    const isRead = status === EvolutionMessageStatusEnum.READ || read
    let updateDoc = {}
    let exists = true
    const doc = await docRef.get()
    if (deleted) {
      exists = doc.exists
      updateDoc = {
        deleted,
        file: null,
        audio: null,
        video: null,
        image: null,
      }
    } else {
      updateDoc = {
        read: isRead,
        status,
      }
    }
    if (exists) {
      if (
        status == 'DELIVERY_ACK' &&
        doc.data()?.shotxCron &&
        !doc.data()?.status
      ) {
        this.addCronLogs(doc.data(), TRIGGERS.DELIVERED)
      } else if (doc.data()?.shotxCron) {
        this.addCronLogs(doc.data(), TRIGGERS.READ)
      }
      await docRef.set(updateDoc, { merge: true })
    }

    if (deleted || isRead) {
      this.deleteCount(accountId, instanceId, phoneNumber)
    }
  }

  addCronLogs = async (message: any, type: string) => {
    saveShotxCronLogs(
      message.shotxCron.id,
      message.accountId,
      message.instanceId,
      message.codechat.contact_id,
      type,
      message.id
    )
    return
  }

  getMessageDocReference = (
    accountId: string,
    instanceId: string,
    phoneNumber: string,
    messageId: string
  ) => {
    const whatsappDocRef = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)
      .collection(FIRESTORE_COLLECTIONS.CONTACTS)
      .doc(phoneNumber)
      .collection(FIRESTORE_COLLECTIONS.MESSAGES)
      .doc(messageId)

    return whatsappDocRef
  }

  saveMessage = async (
    accountId: string,
    instanceId: string,
    phoneNumber: string,
    messageId: string,
    messageDoc: any,
    update = false
  ) => {
    if (!accountId || !instanceId || !phoneNumber || !messageId) {
      const data = {
        error: true,
        data: 'instanceId, contact and message are required',
      }
      console.log(
        'saveMessage !accountId || !instanceId || !phoneNumber || !messageId'
      )
      return data
    }
    try {
      const messageRef = this.getMessageDocReference(
        accountId,
        instanceId,
        phoneNumber,
        messageId
      )
      await messageRef.set(messageDoc, { merge: update })
    } catch (error) {
      console.log('MessageDoc', messageDoc)
      console.error('Error saving message', error)
    }
  }

  saveFailMessage = async (
    accountId: string,
    instanceId: string,
    phoneNumber: string,
    messageDoc: any
  ) => {
    const messageRef = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)
      .collection(FIRESTORE_COLLECTIONS.CONTACTS)
      .doc(phoneNumber)
      .collection(FIRESTORE_COLLECTIONS.MESSAGES)
      .doc()

    const docToSave = {
      id: messageRef.id,
      ...messageDoc,
    }
    await messageRef.set(docToSave, { merge: true })
  }

  sanitizePhoneNumber(phoneNumber: string) {
    const onlyNumbers = phoneNumber.replace(/[^0-9]/g, '')
    const cc = onlyNumbers.slice(0, 2) // Country code
    const phone = onlyNumbers.slice(2) // Phone number with area code
    // If phone number is less than 11 digits and country code is 55, add the nine digit
    if (phone.length < 11 && cc === '55') {
      const ddd = phone.slice(0, 2)
      const num = phone.slice(-8)
      return `${cc}${ddd}9${num}`
    }
    return `${cc}${phone}`
  }

  getPhoneNumber = (remoteJid: string) => {
    let phone = remoteJid
    if (phone.includes(':')) {
      phone = phone.split(':')[0]
    } else {
      phone = remoteJid.split('@')[0]
    }

    return this.sanitizePhoneNumber(phone)
  }

  getDate = (messageTimestamp: any) => {
    return dateToString(dateFromNum(Number(messageTimestamp)), 'YYYYMMDD')
  }

  getMessageData = (
    messageData: any
  ): {
    timestamp: any
    date: any
    read: boolean
    fromMe: boolean
    phoneNumber: string
    messageId: string
  } => {
    // console.log('Getting Message Data', messageData)
    const { messageTimestamp, key, data, keyId, remoteJid, fromMe } =
      messageData as any

    // TODO: verificar o status
    const read = false //messageData.status === EvolutionMessageStatusEnum.READ

    const isFromMe = (key?.fromMe || fromMe || data?.fromMe) == true
    const messageId = key?.id || keyId || data?.id
    const phoneNumber = this.getPhoneNumber(
      key?.remoteJid || remoteJid || data?.remoteJid
    )

    return {
      timestamp: messageTimestamp,
      date: this.getDate(messageTimestamp),
      read,
      fromMe: isFromMe,
      phoneNumber,
      messageId,
    }
  }

  updateInstanceStatus = async (
    instanceStatus: instanceStatusDto,
    create = false
  ) => {
    const { accountId, status, instanceId } = instanceStatus
    if (!accountId || !status || !instanceId) {
      return
    }

    const instanceDoc = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)

    const instanceRef = await instanceDoc.get()

    if (!instanceRef.exists && !create) {
      return
    }

    const updatedAt = moment().unix()
    await instanceDoc.set({ status, updatedAt }, { merge: true })
  }

  getInstance = async (
    accountId: string,
    instanceId: string
  ): Promise<instanceCreateType | null> => {
    const instanceDoc = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)

    const instanceRef = await instanceDoc.get()

    if (!instanceRef.exists) {
      return null
    }

    return instanceRef.data() as instanceCreateType
  }

  getLastFromMe = async (
    accountId: string,
    instanceId: string,
    contactRemoteId: string
  ): Promise<any | null> => {
    const whatsappDocRef = await firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)
      .collection(FIRESTORE_COLLECTIONS.CONTACTS)
      .doc(contactRemoteId)
      .collection(FIRESTORE_COLLECTIONS.MESSAGES)
      .where('sender', '==', true)
      .orderBy('timestamp', 'desc')
      .limit(1)
      .get()

    if (whatsappDocRef.empty) {
      return null
    }

    const whatsappDocs: any = []

    whatsappDocRef.forEach((doc) => {
      whatsappDocs.push(doc.data())
    })

    return whatsappDocs[0]
  }

  saveCount = async (
    accountId: string,
    instanceId: string,
    phoneNumber: string
  ) => {
    const counter = {
      accountId,
      phoneNumber,
      context: 'shotx',
      instanceId,
      read: false,
      type: 'message',
      createdAt: moment().unix(),
      updatedAt: moment().unix(),
    }

    const counterRef = firestore.collection(FIRESTORE_COLLECTIONS.COUNTERS)

    await counterRef.add(counter)
  }

  updateCount = async (
    accountId: string,
    instanceId: string,
    phoneNumber: string,
    data: any
  ) => {
    const counterRef = firestore
      .collection(FIRESTORE_COLLECTIONS.COUNTERS)
      .where('accountId', '==', accountId)
      .where('instanceId', '==', instanceId)
      .where('phoneNumber', '==', phoneNumber)

    const querySnapshot = await counterRef.get()

    querySnapshot.forEach((doc) => {
      doc.ref.set({ ...data, updatedAt: moment().unix() }, { merge: true })
    })
  }

  deleteCount = async (
    accountId: string,
    instanceId: string,
    phoneNumber: string
  ) => {
    const counterRef = firestore
      .collection(FIRESTORE_COLLECTIONS.COUNTERS)
      .where('accountId', '==', accountId)
      .where('instanceId', '==', instanceId)
      .where('phoneNumber', '==', phoneNumber)

    const querySnapshot = await counterRef.get()

    querySnapshot.forEach((doc) => {
      doc.ref.delete()
    })
  }
}
