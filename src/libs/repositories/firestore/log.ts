import moment from 'moment'
import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { findDifference } from '../../../utils/objectUtils'

export class LogsRepository {
  public createInstanceLog = async (
    instance: any,
    trigger: string,
    user: any,
    oldLogs?: any
  ) => {
    let changed = null

    if (oldLogs) {
      changed = findDifference(instance, oldLogs)
    }

    const log = {
      accountId: instance.accountId,
      collection: 'instances',
      context: {},
      createdAt: moment().unix(),
      collections: ['instances', 'shotx'],
      data: {
        accountId: instance.accountId,
        status: instance?.status,
        author: user?.uid || 0,
        changed: changed,
      },
      operator_id: user?.uid || 0,
      trigger: trigger,
      updatedAt: moment().unix(),
      user_id: user?.uid || 0,
    }

    await firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(instance.accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instance.id)
      .collection('logs')
      .add(log)
  }
}
