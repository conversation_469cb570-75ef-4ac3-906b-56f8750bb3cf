import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { instanceModel } from '../../model/instance'
import { instanceCreateType } from '../../types/codechat/instanceCreate'
import { instanceUpdateType } from '../../types/codechat/instanceUpdate'
import { InstanceType } from '../../types/instance/instance'

export class InstancesRepository {
  public createInstance = async (instance: instanceUpdateType) => {
    const { accountId } = instance
    const response = {
      error: false,
      message: '',
      data: null as instanceCreateType | null,
    }

    if (!accountId) {
      response.error = true
      response.message = 'Account not found'
      return response
    }

    const instanceSave = {
      ...instanceModel,
      ...instance,
    }

    const instanceDoc = await firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .add(instanceSave)

    return instanceDoc
      .update({ id: instanceDoc.id, ID: instanceDoc.id })
      .then(() => {
        console.log('Document successfully updated!')
        response.data = {
          ...instanceSave,
          id: instanceDoc.id,
          ID: instanceDoc.id,
        }

        return response
      })
      .catch((error) => {
        console.error('Error updating document: ', error)
        response.error = true
        response.message = error
        return response
      })
  }

  public update = async (instance: any) => {
    const response = {
      error: false,
      message: '',
      data: null as instanceUpdateType | null,
      oldInstance: null as instanceUpdateType | null,
    }

    const { accountId, id } = instance
    const instanceDoc = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(id)

    // let oldInstance = ''
    const oldInstance = (await instanceDoc.get()).data() || {}
    response.oldInstance = oldInstance as instanceUpdateType

    let instanceUpdated = null

    instanceUpdated = await instanceDoc
      .set(instance, { merge: true })
      .then(() => {
        console.log('Document successfully updated!')
        response.data = instance
        return response
      })
      .catch((error) => {
        console.error('Error updating document: ', error)
        response.error = true
        response.message = error
        return response
      })

    return instanceUpdated
  }

  public get = async (accountId: string, instanceId: string) => {
    const response = {
      error: false,
      message: '',
      data: null as InstanceType | null,
    }

    const instanceDoc = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)

    const instance = await instanceDoc.get()
    response.data = instance.data() as InstanceType

    return response
  }

  public getInstances = async (accountId: string) => {
    // get collection reference
    const instanciesRef = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)

    // get snapshot
    const snapshot = await instanciesRef
      .where('accountId', '==', accountId)
      .orderBy('createdAt', 'desc')
      .get()

    // se não tiver dados
    if (snapshot.empty) {
      console.log('No matching documents.')
      return []
    }

    // array de instancies
    const instancies: any = []
    snapshot.forEach((doc) => {
      instancies.push(doc.data())
    })

    // orderna e retorna o primeiro instanceName
    const instanciesSorted = instancies.sort(
      (a: any, b: any) => a.createdAt - b.createdAt
    )
    return instanciesSorted
  }

  public findByInstagramId = async (
    accountId: string,
    ig_id: string
  ): Promise<any> => {

    try {
      const instancesRef = await firestore
        .collection(FIRESTORE_COLLECTIONS.SHOTX)
        .doc(accountId)
        .collection(FIRESTORE_COLLECTIONS.INSTANCES)
        .where('ig_id', '==', ig_id)
        .where('platform', '==', 'Instagram')
        .where('status.state', '==', 'open') // Não traz instancia desconectada (consequentemente não salva eventos e mensagens)
        .get()

      if (!instancesRef.empty) {
        return instancesRef.docs[0].data()
      } else {
        return null
      }
    } catch (error) {
      console.error('Error fetching account:', error)
      return null
    }
  }

  public findInstanceByIgSid = async (ig_sid: string): Promise<any> => {
    try {
      const snapshot = await firestore
        .collection(FIRESTORE_COLLECTIONS.ACCOUNTS)
        .where('ig_ids', 'array-contains', ig_sid)
        .get()

      for (const account of snapshot.docs) {
        const instance = await this.findByInstagramId(account.id, ig_sid)
        if (instance) return instance
      }

      return null
    } catch (error) {
      console.error('Error fetching account:', error)
      return null
    }
  }
}
