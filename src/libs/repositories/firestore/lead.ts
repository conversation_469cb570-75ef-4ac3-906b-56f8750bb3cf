import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { mergeObject } from '../../../utils/objectUtils'
import { CloudFunctionsAPI } from '../../services/firestore/cloudFunctionsApi'
import LeadType from '../../types/qiplus/lead'

export class LeadRepository {
  private collectionName = FIRESTORE_COLLECTIONS.LEADS
  private leadsRef: FirebaseFirestore.CollectionReference = firestore.collection(this.collectionName)
  private queuesRef: FirebaseFirestore.CollectionReference = firestore.collection(FIRESTORE_COLLECTIONS.QUEUES)
  private queueTime = 60 // in seconds

  constructor() { }

  public leads = (accountId: string) => {
    return this.leadsRef
      .where('accountId', '==', accountId)
      .where('status', '!=', 'trash')
  }

  public exists = async (lead: LeadType) => {
    try {
      const leadDocs = await this.leadsRef
        .where('mobile', '==', lead.mobile)
        .where('accountId', '==', lead.accountId)
        .where('status', '!=', 'trash')
        .get()
        .then((querySnapshot: any) => {
          const leadIds = querySnapshot.docs.map((lead: any) => {
            const leadData = lead.data()
            if (leadData.id) {
              return leadData.id
            }
          })

          return leadIds
        })

      if (leadDocs[0]) {
        return leadDocs[0]
      }

      return false
    } catch (error: any) {
      console.log('🚀 - Failed to check if lead already exists: ', error)
      return false
    }
  }

  public createLead = async (lead: any) => {
    try {
      const cloudAPI = new CloudFunctionsAPI()
      return await cloudAPI.post('createLead', lead)
    } catch (error: any) {
      console.log('🚀 - Failed to create Lead: ', error)
      return null
    }
  }

  public all = async () => {
    const snapshot = await this.leadsRef.get()
    return snapshot.docs.map((doc) => doc.data())
  }

  public get = async (leadId: string): Promise<any | null> => {
    let leadsRef = this.leadsRef.doc(leadId)

    try {
      // Executa a consulta
      const querySnapshot = await leadsRef.get();
      if (!querySnapshot.exists) return null

      // Retorna os documentos encontrados
      return querySnapshot.data()
    } catch (error: any) {
      console.log(`🚀 - Failed to get lead ${leadId}: `, error);
      return null;
    }
  }

  public findByEmail = async (accountId: string, email: string): Promise<any[]> => {
    try {
      // Executa a consulta
      const querySnapshot = await this.leads(accountId)
        .where('email', '==', String(email)).get();

      // Retorna os documentos encontrados
      return querySnapshot.docs.map((lead: any) => mergeObject(lead.data(), { id: lead.id, ID: lead.id }));
    } catch (error: any) {
      console.log(`🚀 - Failed to get lead by email ${email}: `, error);
      return [];
    }
  }

  public findByPhone = async (accountId: string, phone: string, countryCode: string): Promise<any[]> => {
    try {
      // Executa a consulta
      const querySnapshot = await this.leads(accountId)
        .where('phone', '==', String(phone))
        .where('phoneCC', '==', String(countryCode))
        .get();

      // Retorna os documentos encontrados
      return querySnapshot.docs.map((lead: any) => mergeObject(lead.data(), { id: lead.id, ID: lead.id }));
    } catch (error: any) {
      console.log(`🚀 - Failed to get lead by phone ${phone}: `, error);
      return [];
    }
  }

  public findByMobile = async (accountId: string, phone: string, countryCode: string): Promise<any[]> => {
    try {
      // Executa a consulta
      const querySnapshot = await this.leads(accountId)
        .where('mobile', '==', String(phone))
        .where('mobileCC', '==', String(countryCode))
        .get();

      // Retorna os documentos encontrados
      return querySnapshot.docs.map((lead: any) => mergeObject(lead.data(), { id: lead.id, ID: lead.id }));
    } catch (error: any) {
      console.log(`🚀 - Failed to get lead by mobilePhone ${phone}: `, error);
      return [];
    }
  }

  public getWhere = async (accountId: string, wheres: any): Promise<any[]> => {
    let leadsRef = this.leads(accountId)

    try {
      if (wheres && Array.isArray(wheres) && wheres.length > 0) {
        for (const w of wheres) {
          leadsRef = leadsRef.where(w[0], w[1], w[2]); // Aplica as condições ao leadsRef
        }
      }

      // Executa a consulta
      const querySnapshot = await leadsRef.get();

      // Retorna os documentos encontrados
      return querySnapshot.docs.map((lead: any) => mergeObject(lead.data(), { id: lead.id, ID: lead.id }));
    } catch (error: any) {
      console.log(`🚀 - Failed to get leads where ${wheres}: `, error);
      return [];
    }
  }

  public isCreating = async (lead: any) => {
    try {
      const queuesDocs = await this.queuesRef
        .where('mobile', '==', lead.mobile)
        .where('accountId', '==', lead.accountId)
        .get()

      if (queuesDocs.size > 0) {
        const now = Date.now()
        queuesDocs.docs.forEach((doc) => {
          if (this.evaluateTimeout(doc.createTime)) {
            doc.ref.delete()
          }
        })
        return true
      }

      return false
    } catch (error: any) {
      console.log('🚀 - Failed to check if lead already exists: ', error)
      return false
    }
  }

  public evaluateTimeout = (createdAt: any) => {
    const createdDate = new Date(createdAt)
    const currentDate = new Date(Date.now())

    const timelapse = ((currentDate.getTime() - createdDate.getTime() + 59) / 1000)
    return timelapse >= this.queueTime
  }

  public update = async (lead: LeadType) => {
    try {
      const { id } = lead
      this.leadsRef
        .doc(id)
        .set(lead, { merge: true })
    } catch (error: any) {
      console.log('🚀 - Failed to update lead: ', error)
    }
  }

}
