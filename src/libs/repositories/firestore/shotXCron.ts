import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'

export class ShotXCronRepository {

  private collectionName = FIRESTORE_COLLECTIONS.SHOTXCRON
  private shotXCronRef: FirebaseFirestore.CollectionReference = firestore.collection(this.collectionName)

  constructor() { }

  public update = (id: string, data: any) => {

    try {
      this.shotXCronRef
        .doc(id)
        .set(data, { merge: true })
    } catch (error: any) {
      console.log('🚀 - Failed to update shotxCron: ', error)
    }
  }
}