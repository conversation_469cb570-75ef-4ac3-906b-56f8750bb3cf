import axios, { AxiosInstance } from 'axios'
import { cloudfunctionsBaseURL } from '../../../config'

export class CloudFunctionsAPI {
  private axiosRequest: AxiosInstance
  constructor() {
    this.axiosRequest = axios.create({
      baseURL: cloudfunctionsBaseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  async post(remoteFunction: string, data: any) {
    try {
      const response = await this.axiosRequest.post(remoteFunction, {
        data,
      })

      return response
    } catch (error: any) {
      console.log(
        '🚀 - file: cloudFunctionsApi.ts:19 - CloudFunctionsAPI - post - error:',
        error
      )
    }
  }
}
