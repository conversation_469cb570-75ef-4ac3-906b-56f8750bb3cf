import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { InstagramErrors } from '../../../errors/InstagramErrors'
import { waitMiliseconds } from '../../../utils/time'
const {
  INSTAGRAM_URL,
  INSTAGRAM_API_URL,
  INSTAGRAM_GRAPH_API_URL,
  INSTAGRAM_GRAPH_API_VERSION,
  INSTAGRAM_CLIENT_ID,
  INSTAGRAM_CLIENT_SECRET,
  INSTAGRAM_REDIRECT_URI_CALLBACK,
  FACEBOOK_GRAPH_API_URL,
} = process.env

export class InstagramService {
  private graphAPI: AxiosInstance

  constructor() {
    this.graphAPI = axios.create({
      baseURL: `${INSTAGRAM_GRAPH_API_URL}/${INSTAGRAM_GRAPH_API_VERSION}`,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  setHeadersAccessToken(accessToken: string | null) {
    if (accessToken) {
      this.graphAPI.defaults.headers.common.Authorization = `Bearer ${accessToken}`
    } else {
      delete this.graphAPI.defaults.headers.common.Authorization
    }
  }

  setGraphApiRequestParams(params: any) {
    this.graphAPI.defaults.params = params || {}
  }

  apiWithParams(params: any, instance?: AxiosInstance): AxiosInstance {
    if (instance) {
      instance.defaults.params = params || {}
      return instance
    }

    this.graphAPI.defaults.params = params || {}
    return this.graphAPI
  }

  async getAuthenticationUrl(
    accountId: string,
    instanceId: string
  ): Promise<any> {
    const scopes = [
      'business_basic',
      'business_manage_messages',
      'business_manage_comments',
      //'business_content_publish',
    ]
    return `${INSTAGRAM_URL}/oauth/authorize?client_id=${INSTAGRAM_CLIENT_ID}&redirect_uri=${INSTAGRAM_REDIRECT_URI_CALLBACK}&response_type=code&scope=${scopes.join('%2C')}&state=accountId${accountId}_instanceId${instanceId}`
  }

  async getUserProfile(accessToken: string): Promise<any> {
    const options = {
      method: 'GET',
      url: `${INSTAGRAM_GRAPH_API_URL}/${INSTAGRAM_GRAPH_API_VERSION}/me`,
      params: {
        fields: 'id,user_id,name,username,account_type,profile_picture_url',
        access_token: accessToken,
      },
    }

    let result: any
    try {
      const response: AxiosResponse = await axios.request(options)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('GET USER PROFILE ERROR', error)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error,
        },
      }
    }
    return result
  }

  async getUserToken(code: string): Promise<any> {
    const options = {
      method: 'POST',
      url: `${INSTAGRAM_API_URL}/oauth/access_token`,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: {
        client_id: INSTAGRAM_CLIENT_ID,
        client_secret: INSTAGRAM_CLIENT_SECRET,
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: INSTAGRAM_REDIRECT_URI_CALLBACK,
      },
    }

    let result: any
    try {
      const response: AxiosResponse = await axios.request(options)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('GET USER TOKEN ERROR', error)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error,
        },
      }
    }
    return result
  }

  async getLongLivedToken(accessToken: string): Promise<any> {
    const options = {
      method: 'GET',
      url: `${INSTAGRAM_GRAPH_API_URL}/access_token`,
      params: {
        access_token: accessToken,
        client_id: INSTAGRAM_CLIENT_ID,
        client_secret: INSTAGRAM_CLIENT_SECRET,
        grant_type: 'ig_exchange_token',
      },
    }

    let result: any
    try {
      const response: AxiosResponse = await axios.request(options)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('GET USER LONG TOKEN ERROR', error)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error,
        },
      }
    }
    return result
  }

  async subscribeToWebhook(
    access_token: string,
    subscribes: string[]
  ): Promise<any> {
    if (!access_token || !subscribes) {
      return
    }

    const params = {
      subscribed_fields: subscribes.join(','),
      access_token,
    }

    let result: any
    try {
      const response: AxiosResponse = await this.apiWithParams(params).post(
        '/me/subscribed_apps'
      )
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('SUBSCRIBE TO WEBHOOK ERROR', error)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error,
        },
      }
    }
    return result
  }

  async unsubscribeToWebhook(access_token: string): Promise<any> {
    if (!access_token) {
      return
    }
    const params = {
      access_token,
    }

    let result: any
    try {
      const response: AxiosResponse = await this.apiWithParams(params).delete(
        '/me/subscribed_apps'
      )
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('UNSUBSCRIBE TO WEBHOOK ERROR', error)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error,
        },
      }
    }
    return result
  }

  async logout(access_token: string): Promise<any> {
    if (!access_token) {
      return
    }
    const params = {
      access_token,
    }

    let result: any
    try {
      const response: AxiosResponse = await this.apiWithParams(params).delete(
        '/me/subscribed_apps'
      )
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('UNSUBSCRIBE TO WEBHOOK ERROR', error)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error,
        },
      }
    }
    return result
  }

  async getMessageDataFromMessageId(
    messageId: string,
    accessToken: string
  ): Promise<any> {
    const fields = ['id', 'created_time', 'from', 'to']
    const url = `/${messageId}?fields=${fields.join(',')}&access_token=${accessToken}`

    let result: any
    try {
      const response: AxiosResponse = await this.graphAPI.get(url)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('GET MESSAGE DATA FROM MESSAGE ID ERROR', error.data)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error,
        },
      }
    }
    return result
  }

  async getUserDataFromSenderID(
    senderId: string,
    access_token: string
  ): Promise<any> {
    const params = {
      access_token,
    }

    let result: any
    try {
      const response: AxiosResponse = await this.apiWithParams(params).get(
        `/${senderId}`
      )
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('GET USER DATA ERROR', error)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error,
        },
      }
    }
    return result
  }
  async getUsernameFromIgID(ig_id: string, accessToken: string): Promise<any> {
    const url = `/${ig_id}?access_token=${accessToken}`
    let result: any
    try {
      const response: AxiosResponse = await this.graphAPI.get(url)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('GET USERNAME FROM SENDER ID ERROR', error)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error,
        },
      }
    }
    return result
  }

  async sendMessageText(
    senderId: string,
    message: string,
    access_token: string,
    delay_time?: number
  ): Promise<any> {
    if (!senderId || !message || !access_token) {
      console.log('SENDERID, MESSAGE AND ACCESSTOKEN REQUIRED TO SEND MESSAGE')
      return {
        error: true,
        message: 'SENDERID, MESSAGE AND ACCESSTOKEN REQUIRED TO SEND MESSAGE',
      }
    }
    console.log('sendMessage', message, senderId, access_token)

    const params = {
      access_token,
      tags: 'HUMAN_AGENT',
    }

    const body = {
      recipient: {
        id: senderId,
      },
      message: {
        text: message,
      },
    }

    if (delay_time) {
      await new Promise((resolve) => setTimeout(resolve, delay_time))
    }

    return await this.apiWithParams(params)
      .post(`/me/messages`, body)
      .then((res) => {
        return {
          error: false,
          status: res?.status ? res?.status : '',
          data: {
            message: res.data,
          },
        }
      })
      .catch((err) => {
        console.log('ERROR', err)
        const instagramErrors = new InstagramErrors({
          error: true,
          status: err?.response?.status ? err?.response?.status : '',
          data: {
            message: err.response.data,
          },
        })
        return instagramErrors.sendMessage()
      })
  }

  async sendAttachment(
    senderId: string,
    type: string,
    url: string,
    access_token: string,
    delay?: number
  ): Promise<any> {
    if (!senderId || !type || !url || !access_token) {
      console.log('SENDERID, TYPE, URL AND ACCESSTOKEN REQUIRED TO SEND MEDIA')
      return
    }

    const params = {
      access_token,
      tags: 'HUMAN_AGENT',
    }

    const body = {
      recipient: {
        id: senderId,
      },
      message: {
        attachment: {
          type,
          payload: {
            url,
          },
        },
      },
    }

    if (delay) {
      await waitMiliseconds(delay)
    }

    return await this.apiWithParams(params)
      .post(`/me/messages`, body)
      .then((res) => {
        return {
          error: false,
          status: res?.status ? res?.status : '',
          data: {
            message: res.data,
          },
        }
      })
      .catch((err) => {
        return {
          error: true,
          status: err?.response?.status ? err?.response?.status : '',
          data: {
            message: err,
          },
        }
      })
    //console.log('MESSAGE SEND RESULT', messageResponse)
  }

  async sendTemplate(
    senderId: string,
    elements: Option[],
    access_token: string
  ): Promise<any> {
    if (!senderId || !elements || !access_token) {
      console.log('SENDERID, TYPE, URL AND ACCESSTOKEN REQUIRED TO SEND MEDIA')
      return
    }

    const params = {
      access_token,
      tags: 'HUMAN_AGENT',
    }

    const body = {
      recipient: {
        id: senderId,
      },
      message: {
        attachment: {
          type: 'template',
          payload: {
            template_type: 'generic',
            elements,
          },
        },
      },
    }

    return await this.apiWithParams(params)
      .post(`/me/messages`, body)
      .then((res) => {
        return {
          error: false,
          status: res?.status ? res?.status : '',
          data: {
            message: res.data,
          },
        }
      })
      .catch((err) => {
        return {
          error: true,
          status: err?.response?.status ? err?.response?.status : '',
          data: {
            message: err,
          },
        }
      })
  }

  async sendButton(
    senderId: string,
    buttons: Option[],
    access_token: string,
    delay?: number
  ): Promise<any> {
    if (!senderId || !buttons || !access_token) {
      console.log('SENDERID, BUTTONS AND ACCESSTOKEN REQUIRED TO SEND BUTTON')
      return
    }

    const params = {
      access_token,
      tags: 'HUMAN_AGENT',
    }

    const body = {
      recipient: {
        id: senderId,
      },
      message: {
        attachment: {
          type: 'template',
          payload: {
            template_type: 'button',
            text: 'Menu', // Titulo acima dos botões (geralmente uma pergunta)
            buttons,
          },
        },
      },
    }

    if (delay) {
      await waitMiliseconds(delay)
    }

    return await this.apiWithParams(params)
      .post(`/me/messages`, body)
      .then((res) => {
        return {
          error: false,
          status: res?.status ? res?.status : '',
          data: {
            message: res.data,
          },
        }
      })
      .catch((err) => {
        return {
          error: true,
          status: err?.response?.status ? err?.response?.status : '',
          data: {
            message: err,
          },
        }
      })
  }
}

type Option = {
  title: string
  image_url: string
  subtitle: string
  default_action: {
    type: string
    url: string
  }
  buttons: Button[]
}

type Button = {
  type: string
  url: string
  title: string
}

export const InstagramServiceType = typeof InstagramService
