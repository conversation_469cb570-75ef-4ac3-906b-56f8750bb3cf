import LeadType from '../../qiplus/lead'

export type MessageUpSert = {
  event: string
  instance: string
  data: MessageUpSertData
  dateTime: Date
  sender: string
  serverURL: string
  apikey: string
  lead: LeadType
  instanceId: string // Adicionado pelo Middleware
  accountId: string // Adicionado pelo Middleware
  contactId: string // Adicionado pelo Middleware
  phoneNumber: string // Adicionado pelo Middleware
  phone: string // Adicionado pelo Middleware
  phoneCC: string // Adicionado pelo Middleware
  message: string // Adicionado pelo Middleware
}

export type MessageMediaResponse = {
  key: MessageKey
  message: Message
  messageTimestamp: number
  status: string
}

export type MessageUpSertData = {
  key: MessageKey
  pushName: string
  firstName: string
  lastName: string
  message: Message
  contextInfo: null
  messageType: string
  messageTimestamp: number
  instanceID: string
  source: string
  status?: string
  isGroup?: boolean
  messageId: string
  keyId: string
  remoteJid: string
  fromMe: boolean
  participant: string
  instanceId: string
  phone: string
  accountId: string
  countryCode: string
  locale: string
}

export type MessageKey = {
  remoteJid: string
  fromMe: boolean
  id: string
  status?: string
}

export type Message = {
  conversation: string
  extendedTextMessage: ExtendedTextMessage
  imageMessage: ImageMessage
  documentMessage: DocumentMessage
  videoMessage: VideoMessage
  audioMessage: AudioMessage
  messageContextInfo: MessageContextInfo
  mediaURL: string
}

export type MessageContextInfo = {
  deviceListMetadata: DeviceListMetadata
  deviceListMetadataVersion: number
}

export type DeviceListMetadata = {
  senderKeyHash: string
  senderTimestamp: string
  recipientKeyHash: string
  recipientTimestamp: string
}

export type ExtendedTextMessage = {
  text: string
}

export type DocumentMessage = {
  url: string
  mimetype: string
  title: string
  fileSha256: string
  fileLength: string
  pageCount: number
  mediaKey: string
  fileName: string
  fileEncSha256: string
  directPath: string
  mediaKeyTimestamp: string
  contactVcard: boolean
  jpegThumbnail: string
}

export type VideoMessage = {
  url: string
  mimetype: string
  fileSha256: string
  fileLength: string
  seconds: number
  mediaKey: string
  height: number
  width: number
  fileEncSha256: string
  directPath: string
  mediaKeyTimestamp: string
  jpegThumbnail: string
  streamingSidecar: string
}

export type AudioMessage = {
  url: string
  mimetype: string
  fileSha256: string
  fileLength: string
  seconds: number
  ptt: boolean
  mediaKey: string
  fileEncSha256: string
  directPath: string
  mediaKeyTimestamp: string
  streamingSidecar: string
  waveform: string
}

export type ImageMessage = {
  url: string
  mimetype: string
  fileSha256: string
  fileLength: string
  height: number
  width: number
  mediaKey: string
  fileEncSha256: string
  directPath: string
  mediaKeyTimestamp: string
  jpegThumbnail: string
  firstScanSidecar: string
  firstScanLength: number
  scansSidecar: string
  scanLengths: number[]
  midQualityFileSha256: string
}
export default MessageUpSert
