export type InstanceCreateResponse = {
  instance: {
    instanceName: string;
    instanceId: string;
    webhookWaBusiness: null;
    accessTokenWaBusiness: string;
    status: string;
  };
  hash: string;
  webhook: {};
  websocket: {};
  rabbitmq: {};
  sqs: {};
  settings: {
    rejectCall: boolean;
    msgCall: string;
    groupsIgnore: boolean;
    alwaysOnline: boolean;
    readMessages: boolean;
    readStatus: boolean;
    syncFullHistory: boolean;
  }
}