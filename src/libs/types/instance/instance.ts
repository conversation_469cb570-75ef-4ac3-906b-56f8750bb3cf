export type InstanceType = {
  accountId: string;
  createdAt: number;
  title: string;
  userId: string;
  platform: string;
  ID: string;
  id: string;
  ig_id: string;
  updatedAt: number;
  auth: InstanceAuth;
  status: InstanceStatus;
  sniperIds: string[];
  subscriptions: string[];
}

export type InstanceAuth = {
  jwt: string;
  permissions: string[];
  longToken: string;
}

export type InstanceStatus = {
  owner: string;
  statusReason: number;
  message: string;
  profile: string;
  account_type: string;
  app_scope_id: string;
  username: string;
  ig_id: string;
  profilePicture: string;
  state: string;
}