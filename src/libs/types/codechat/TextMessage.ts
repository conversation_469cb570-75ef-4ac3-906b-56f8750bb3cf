type TextMessageType = {
  keyId: string
  keyRemoteJid: string
  keyFromMe: boolean
  pushName: string
  content: {
    text: string
    contextInfo: {
      entryPointConversionSource: string
      entryPointConversionApp: string
      entryPointConversionDelaySeconds: number
    }
  }
  isGroup: false
  messageType: string
  messageTimestamp: number
  device: string
}

export default TextMessageType
