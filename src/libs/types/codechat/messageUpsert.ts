import AudioMessageType from './AudioMessage'
import ImageMessageType from './ImageMessage'
import TextMessageType from './TextMessage'

type MessageUpSert = {
  event: string
  instance: string
  data: {
    keyId: string
    keyRemoteJid: string
    keyFromMe: boolean
    pushName: string
    message: TextMessageType | ImageMessageType | AudioMessageType
    messageTimestamp: number
    owner: string
    source: string
  }
}

export default MessageUpSert
