type AudioMessageType = {
  keyId: string
  keyRemoteJid: string
  keyFromMe: boolean
  pushName: string
  content: {
    contextInfo: ObjectConstructor[]
    directPath: string
    fileEncSha256: string
    fileLength: string
    fileSha256: string
    mediaKey: string
    mediaKeyTimestamp: string
    mimetype: string
    ptt: boolean
    seconds: number
    url: string
    viewOnce: boolean
    waveform: string
  }
  messageType: string
  messageTimestamp: number
  owner: string
  source: string
}

export default AudioMessageType
