export const messageExample = {
  object: 'instagram',
  entry: [
    {
      time: 1723043537904,
      id: '17841411687200469',
      messaging: [
        {
          sender: {
            id: '483423407729576',
          },
          recipient: {
            id: '17841411687200469',
          },
          timestamp: 1723043535643,
          message: {
            mid: 'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDExNjg3MjAwNDY5OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTY1MTk0MDAzMDc4ODYwOTozMTc4NDU0MzEyOTg3ODg4MzMwNjIyNDA3MjM4NzQ2MTEyMAZDZD',
            text: 'Opa, quero saber mais',
          },
          reaction: {
            mid: "aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDU5NjI3MzYyMzY3OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM3MjI3MzY5NTQyNjc0MDozMTgzMDg0NDc0MjcwNzM4MTA5MDkyNTEyMzQ4MTg5NDkxMgZDZD",
            action: "react",
            reaction: "other",
            emoji: "❤"
          },
          read: {
            mid: "aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDU5NjI3MzYyMzY3OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTM3MjI3MzY5NTQyNjc0MDozMTgzMDg0NDc0MjcwNzM4MTA5MDkyNTEyMzQ4MTg5NDkxMgZDZD",
          },
          postback: {
            title: 'RECEBER CUPOM',
            payload: 'PAYLOAD_TO_INCLUDE_FOR_BUTTON_2',
            mid: 'aWdfZAG1faXRlbToxOklHTWVzc2FnZAUlEOjE3ODQxNDU5NjI3MzYyMzY3OjM0MDI4MjM2Njg0MTcxMDMwMTI0NDI1OTcxOTYwNTg2NDAyNDIyNDozMTg0OTExNjk1NzI1NjQxMTQ4OTY3Mzc3MTQ4OTQyNzQ1NgZDZD'
          }
        },
      ],
    },
  ],
}
export type InstagramMessageEvent = {
  object: string
  entry: InstagramMessageEntry[]
}

export type InstagramMessageEntry = {
  time: number
  id: string
  messaging: InstagramMessage[]
}
export type InstagramMessage = {
  sender: {
    id: string
  }
  recipient: {
    id: string
  }
  timestamp: number
  message: {
    mid: string
    text: string
  },
  reaction: InstagramMessageReaction,
  read: InstagramMessageRead,
  postback: InstagramMessagePostback
}

export type InstagramMessageReaction = {
  mid: string;
  action: string;
  reaction: string;
  emoji: string;
};

export type InstagramMessageRead = {
  mid: string;
};

export type InstagramMessagePostback = {
  title: string;
  payload: string;
  mid: string;
}