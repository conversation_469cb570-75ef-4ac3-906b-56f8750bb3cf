export const commentExample = {
  entry: [
    {
      id: '17841411687200469',
      time: 1723043493,
      changes: [
        {
          value: {
            from: {
              id: '483423407729576',
              username: 'paulowender',
            },
            media: {
              id: '18070677583318230',
              media_product_type: 'FEED',
            },
            id: '18322592446199542',
            text: 'O melhor funil de vendas',
          },
          field: 'comments',
        },
      ],
    },
  ],
  object: 'instagram',
}

export type InstagramCommentEvent = {
  entry: {
    id: string
    time: number
    changes: {
      value: {
        from: {
          id: string
          username: string
        }
        media: {
          id: string
          media_product_type: string
        }
        id: string
        text: string
      }
      field: string
    }[]
  }[]
  object: string
}
