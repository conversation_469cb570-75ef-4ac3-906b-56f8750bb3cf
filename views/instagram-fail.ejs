<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" type="text/css" href="inddex.css" media="screen" />
  <title>Document</title>
</head>

<body>
  <div class="login-container">
    <div class="login-animation">
      <span class="crossmark"></span>
    </div>
    <p class="error-message">Falha ao realizar o login! Por favor tente novamente</p>
  </div>
</body>
<style>
  /* Container e elementos principais */
  .login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: #f09433;
    /* Cores estilo Instagram */
    background: linear-gradient(45deg,
        #f09433,
        #e6683c,
        #dc2743,
        #cc2366,
        #bc1888);
    font-family: "Arial", sans-serif;
  }

  /* Animação do X (crossmark) */
  .login-animation {
    position: relative;
    width: 70px;
    /* Aumentando a largura do container */
    height: 70px;
    /* Aumentando a altura do container */
    margin-bottom: 20px;
    animation: pop-in 0.5s ease-out forwards;
  }

  .crossmark {
    position: relative;
    width: 70px;
    height: 70px;
  }

  .crossmark::before,
  .crossmark::after {
    content: "";
    position: absolute;
    width: 50px;
    /* Aumentando a largura das barras */
    height: 7px;
    /* Aumentando a espessura das barras */
    background-color: white;
    border-radius: 5px;
    top: 50%;
    left: 50%;
    transform-origin: center;
    transform: translate(-50%, -50%) rotate(45deg);
    opacity: 0;
    animation: draw-x 0.5s ease-out 0.5s forwards;
  }

  .crossmark::after {
    transform: translate(-50%, -50%) rotate(-45deg);
  }

  /* Animação de desenhar o X */
  @keyframes draw-x {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  /* Animação do container principal */
  @keyframes pop-in {
    0% {
      transform: scale(0.5);
      opacity: 0;
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Mensagem de erro */
  .error-message {
    color: white;
    font-size: 18px;
    text-align: center;
    opacity: 0;
    animation: fade-in 1s ease-out 1s forwards;
  }

  /* Animação de fade-in para a mensagem */
  @keyframes fade-in {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }
</style>

</html>
