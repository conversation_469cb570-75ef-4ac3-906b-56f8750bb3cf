{"name": "qiplus-chat-api", "version": "1.1.0", "description": "API to integrate QiPlus Chat and Whatsapp with Codechat", "main": "index.js", "author": "<PERSON> e <PERSON>", "license": "0BSD", "engines": {"node": "20.x"}, "scripts": {"build": "rimraf ./build && tsc", "start:pm2": "npm run build && pm2 start build/app.js --name 'qiplus-chat-api-new'", "start:pm2s": "npm run build && pm2 start build/app.js --name 'qiplus-chat-api'", "start": "node build/app.js", "restart": "npm run stop && npm run start:pm2", "stop": "pm2 stop build/app.js && pm2 delete build/app.js", "dev": "nodemon --watch 'src/**/*.ts' --exec ts-node src/app.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@types/fluent-ffmpeg": "^2.1.26", "@types/multer": "^1.4.11", "@types/react-helmet": "^6.1.11", "amqplib": "^0.10.4", "axios": "^1.6.5", "class-validator": "^0.14.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "ejs": "^3.1.10", "express": "^4.18.2", "firebase-admin": "^12.0.0", "fluent-ffmpeg": "^2.1.3", "helmet": "^7.1.0", "ioredis": "^5.6.1", "libphonenumber-js": "^1.11.11", "minio": "^8.0.1", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "pm2": "^5.3.0", "react-helmet": "^6.1.0", "socket.io": "^4.7.4", "ws": "^8.16.0"}, "devDependencies": {"@types/amqplib": "^0.10.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.11.5", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.1.3", "nodemon": "^3.0.3", "prettier": "^3.2.4", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}