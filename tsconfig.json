{"compilerOptions": {"target": "esnext", "module": "commonjs", "lib": ["esnext", "dom"], "allowJs": true, "outDir": "build", "rootDir": "src", "strict": true, "noImplicitAny": true, "esModuleInterop": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "typeRoots": ["src/types", "node_modules/@types"]}, "include": ["src/**/*.ts"], "exclude": ["node_modules/**/*", ".serverless/**/*", ".webpack/**/*", "_warmup/**/*", ".vscode/**/*", "dist"]}